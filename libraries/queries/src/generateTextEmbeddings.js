const generateTextEmbeddingsForProducts = ({
  accountId,
  providerServiceId,
  eventTable,
  productTable,
  resultTable,
  dryRun = false,
  maxItems = 15000,
  generationProperty = 'description',
  platform = 'shopify',
  projectId = 'optimonk-secure-staging-5c52',
  fallbackProperty,
}) => {
  const events = eventTable.startsWith('(') ? eventTable : `\`${eventTable}\``;
  const products = productTable.startsWith('(') ? productTable : `\`${productTable}\``;
  const results = `\`${resultTable}\``;

  const mostPopularProducts = `
  SELECT
    SAFE_CAST(REPLACE(${
      platform === 'shopify' ? 'productId' : 'variantId'
    }, '"', '') AS STRING) as productId,
    SAFE_CAST(REPLACE(variantId, '"', '') AS STRING) as variantId,
    COUNT(*) AS views
  FROM (
    SELECT
      timestamp AS timestamp,
      providerServiceId,
      ${
        platform === 'shopify'
          ? 'ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "productId")[SAFE_OFFSET(0)].value AS productId,'
          : ''
      }
      ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "variantId")[SAFE_OFFSET(0)].value AS variantId,
      (SELECT ctx.value FROM UNNEST(be.context) as ctx WHERE ctx.name = 'url') as url
    FROM ${events} be,
      UNNEST(be.addresses) ad,
      UNNEST(be.events) ev
    WHERE
      ad.customerAddress IS NOT NULL
      AND ad.provider = 'optimonk'
      AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      AND ev.type = "epv"
  )
  GROUP BY productId, variantId
`;

  const viewedProductAndVariantIds = `
  viewedProductIds AS (
    SELECT *
    FROM (${mostPopularProducts})
    ${
      platform === 'shopify'
        ? 'QUALIFY ROW_NUMBER() OVER(PARTITION BY productId ORDER BY views desc) = 1'
        : ''
    }
  )`;

  return `
    -- Generate text embeddings for ${accountId} - ${providerServiceId}

    CREATE TEMP FUNCTION getLinkText(html STRING)
    RETURNS STRING LANGUAGE js
    OPTIONS (library = ['gs://${projectId}-libs/cheerio.js', 'gs://${projectId}-libs/entities.js']) AS '''
    try {
      let value = cheerio.load(entities.decodeHTML(html));
      value = value.text().trim();
      return value;
    } catch (e) {
      return null;
    }
    ''';


    CREATE TEMP FUNCTION getDescription(description STRING, fallbackDescription STRING, descLength INT64)
    RETURNS STRING AS (
      (SELECT
        CASE
          WHEN fallbackDescription IS NOT NULL AND LENGTH(description) < descLength  AND LENGTH(fallbackDescription) >= descLength THEN fallbackDescription
          ELSE description
        END)
    );

    MERGE INTO
      ${results} as existingEmbeddingResult
    USING (
      WITH
      ${viewedProductAndVariantIds}
      , productProperties AS (
        WITH rawProductProperties AS (
          SELECT
            agg.data.*
          FROM (
            SELECT
              ARRAY_AGG(STRUCT(data) ORDER BY timestamp DESC)[SAFE_OFFSET(0)] agg
            FROM
              ${products} AS data
            WHERE timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY)
            GROUP BY accountId, providerServiceId, id, idType, propertyName
          )
          WHERE
            agg.data.propertyValue IS NOT NULL
        )
        SELECT
          id,
          ARRAY_AGG(
            STRUCT(
                SAFE_CAST(REPLACE(propertyName, '"', '') AS STRING) as propertyName,
                SAFE_CAST(REPLACE(propertyValue, '"', '') AS STRING) as propertyValue
              )
          ) as properties,
        FROM rawProductProperties
        GROUP BY id
      )
      , mostPopularProperties AS (
        SELECT
          productId as id,
          variantId,
          (SELECT SAFE_CAST(REPLACE(propertyValue, '"', '') AS STRING) FROM UNNEST(properties) WHERE propertyName = '${generationProperty}') as dirtyDescription,
          CASE
            WHEN '${fallbackProperty}' != 'undefined'
            THEN (SELECT SAFE_CAST(REPLACE(propertyValue, '"', '') AS STRING) FROM UNNEST(properties) WHERE propertyName = '${fallbackProperty}')
            ELSE NULL
          END AS dirtyFallbackDescription,
          views
        FROM viewedProductIds
        INNER JOIN productProperties ON
          SAFE_CAST(REGEXP_EXTRACT(viewedProductIds.productId, r'(\\d+)') AS STRING) = SAFE_CAST(REGEXP_EXTRACT(productProperties.id, r'(\\d+)') AS STRING)
        GROUP BY id, variantId, dirtyDescription, dirtyFallbackDescription, views
        ORDER BY MAX(views) DESC
      )
      , mostPopularProductDescriptions AS (
        SELECT
          id,
          variantId,
          getDescription(dirtyDescription, dirtyFallbackDescription, 10) as dirtyDescription,
          TO_BASE64((SELECT SHA256(getDescription(dirtyDescription, dirtyFallbackDescription, 10)))) as descriptionHash,
          views
        FROM mostPopularProperties
        GROUP BY id, variantId, dirtyDescription, descriptionHash, views
        ORDER BY views DESC
      )
      , cleanDescriptions AS (
        SELECT
          id,
          variantId,
          TRIM(REGEXP_REPLACE(TRIM(
            REGEXP_REPLACE(
              REGEXP_REPLACE(
                REGEXP_REPLACE(getLinkText(dirtyDescription), r'\\t|\\r', ''), -- Remove \t and \r
                r'\\n+', '\\n' -- Replace multiple newlines with a single newline
              ),
              r'\\s+', ' ' -- Replace multiple whitespace characters with a single space
            )
          ), r'^"|"$', '')) AS description,
          descriptionHash,
          views
        FROM mostPopularProductDescriptions
        ORDER BY views DESC
      )
      , topDescriptions AS (
        SELECT *
        FROM cleanDescriptions
        WHERE LENGTH(description) >= 10
        ORDER BY views DESC
        LIMIT ${maxItems}
      )
      , changedMostPopularVariants AS (
        SELECT
          COALESCE(existingEmbeddings.id, topDescriptions.id) as id,
          topDescriptions.variantId as variantId,
          existingEmbeddings.descriptionHash as descriptionHash,
          existingEmbeddings.textEmbedding as textEmbedding,
        FROM topDescriptions
        INNER JOIN ${results} as existingEmbeddings ON (topDescriptions.id = existingEmbeddings.id)
        WHERE existingEmbeddings.descriptionHash = topDescriptions.descriptionHash AND topDescriptions.description != '' AND existingEmbeddings.variantId != topDescriptions.variantId
      )
      , changedAndMissingEmbeddings AS (
        SELECT
          COALESCE(existingEmbeddings.id, topDescriptions.id) as id,
          topDescriptions.variantId as variantId,
          topDescriptions.descriptionHash as descriptionHash,
          topDescriptions.description as description,
          topDescriptions.views as views
        FROM topDescriptions
        FULL OUTER JOIN ${results} as existingEmbeddings ON (topDescriptions.id = existingEmbeddings.id)
        WHERE (existingEmbeddings.descriptionHash is NULL OR existingEmbeddings.descriptionHash != topDescriptions.descriptionHash)
        AND topDescriptions.description != ''
        ORDER BY views DESC
      )

      , newEmbeddings AS (
        SELECT id, variantId, descriptionHash,
        ${
          dryRun
            ? '(SELECT ARRAY<FLOAT64>[2.0]) as textEmbedding FROM (SELECT id, variantId, views, descriptionHash, description as content FROM changedAndMissingEmbeddings)'
            : `ml_generate_embedding_result as textEmbedding
            FROM ML.GENERATE_EMBEDDING(
              MODEL \`optimonk_vertex_ai.text-gecko-multilingual-embedding_v1\`,
              (SELECT id, variantId, descriptionHash, description as content FROM changedAndMissingEmbeddings),
              STRUCT(TRUE AS flatten_json_output)
            )`
        }
      )
      , newData AS (
          SELECT * from newEmbeddings
          UNION ALL
          SELECT * from changedMostPopularVariants
      )
      SELECT * from newData
      ) AS newEmbeddingResult
    ON newEmbeddingResult.id = existingEmbeddingResult.id
    WHEN MATCHED THEN UPDATE SET
      existingEmbeddingResult.variantId = newEmbeddingResult.variantId,
      existingEmbeddingResult.textEmbedding = newEmbeddingResult.textEmbedding,
      existingEmbeddingResult.descriptionHash = newEmbeddingResult.descriptionHash
    WHEN NOT MATCHED THEN INSERT
      (timestamp, id, variantId, descriptionHash, textEmbedding)
    VALUES
      (CURRENT_TIMESTAMP(), newEmbeddingResult.id, newEmbeddingResult.variantId, newEmbeddingResult.descriptionHash, newEmbeddingResult.textEmbedding)
    `;
};

const generateTextEmbeddingsForExternalData = ({
  accountId,
  providerServiceId,
  eventTable,
  externalPropertiesTable,
  resultTable,
  dryRun = false,
  maxItems = 15000,
  generationProperty = 'description',
  projectId = 'optimonk-secure-staging-5c52',
}) => {
  const events = eventTable.startsWith('(') ? eventTable : `\`${eventTable}\``;
  const externalProperties = externalPropertiesTable.startsWith('(')
    ? externalPropertiesTable
    : `\`${externalPropertiesTable}\``;
  const results = `\`${resultTable}\``;

  return `
    -- Generate text embeddings for ${accountId} - ${providerServiceId}
        CREATE TEMP FUNCTION getLinkText(html STRING)
    RETURNS STRING LANGUAGE js
    OPTIONS (library = ['gs://${projectId}-libs/cheerio.js', 'gs://${projectId}-libs/entities.js']) AS '''
    try {
      let value = cheerio.load(entities.decodeHTML(html));
      value = value.text().trim();
      return value;
    } catch (e) {
      return null;
    }
    ''';

    MERGE INTO
      ${results} as existingEmbeddingResult
    USING (
      WITH
      viewedPages AS (
        SELECT
          COUNT(*) AS views,
          REGEXP_REPLACE(COALESCE(canonicalUrl, url), r'/$', '') as url
        FROM (
          SELECT
            timestamp AS timestamp,
            providerServiceId,
            ARRAY(SELECT SPLIT(pr.value, '?')[SAFE_OFFSET(0)] FROM ev.props AS pr WHERE pr.name = "url")[SAFE_OFFSET(0)] AS url,
            ARRAY(SELECT AS STRUCT pr.value FROM ev.props AS pr WHERE pr.name = "canonicalUrl")[SAFE_OFFSET(0)].value AS canonicalUrl,
          FROM ${events} be,
            UNNEST(be.addresses) ad,
            UNNEST(be.events) ev
          WHERE
            ad.customerAddress IS NOT NULL
            AND ad.provider = 'optimonk'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
            AND ev.type = "pageView"
        )
        GROUP BY url
      )
      , externalProperties AS (
        WITH latestProperties AS (
          SELECT *
          FROM ${externalProperties}
          WHERE
              timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY)
          QUALIFY ROW_NUMBER() OVER (PARTITION BY url ORDER BY timestamp DESC) = 1
        )
        SELECT
          timestamp,
          url,
          properties
        FROM latestProperties
      )
      , mostPopularProductDescriptions AS (
        SELECT
          externalProperties.url as id,
          views,
          (SELECT value FROM UNNEST(externalProperties.properties) WHERE name = '${generationProperty}') as dirtyDescription,
          TO_BASE64((SELECT SHA256(value) FROM UNNEST(externalProperties.properties) WHERE name = '${generationProperty}')) as descriptionHash,
        FROM externalProperties
        INNER JOIN viewedPages ON externalProperties.url = viewedPages.url
        GROUP BY id, views, dirtyDescription, descriptionHash
        ORDER BY MAX(views) DESC
      )
      , cleanDescriptions AS (
        SELECT
          id,
          TRIM(REGEXP_REPLACE(TRIM(
            REGEXP_REPLACE(
              REGEXP_REPLACE(
                REGEXP_REPLACE(getLinkText(dirtyDescription), r'\\t|\\r', ''), -- Remove \t and \r
                r'\\n+', '\\n' -- Replace multiple newlines with a single newline
              ),
              r'\\s+', ' ' -- Replace multiple whitespace characters with a single space
            )
          ), r'^"|"$', '')) AS description,
          descriptionHash,
          views
        FROM mostPopularProductDescriptions
        ORDER BY views DESC
      )
      , topDescriptions AS (
        SELECT *
        FROM cleanDescriptions
        WHERE LENGTH(description) >= 10
        LIMIT ${maxItems}
      )
      , changedAndMissingEmbeddings AS (
        SELECT
          COALESCE(existingEmbeddings.id, topDescriptions.id) as id,
          topDescriptions.descriptionHash as descriptionHash,
          topDescriptions.description as description,
          topDescriptions.views as views
        FROM topDescriptions
        FULL OUTER JOIN ${results} as existingEmbeddings ON (topDescriptions.id = existingEmbeddings.id)
        WHERE (existingEmbeddings.descriptionHash is NULL OR existingEmbeddings.descriptionHash != topDescriptions.descriptionHash)
        AND topDescriptions.description != ''
        ORDER BY views DESC
      )

      , newEmbeddings AS (
        SELECT id, views, descriptionHash, content as description,
        ${
          dryRun
            ? '(SELECT ARRAY<FLOAT64>[2.0]) as textEmbedding FROM (SELECT id, views, descriptionHash, description as content FROM changedAndMissingEmbeddings)'
            : `ml_generate_embedding_result as textEmbedding
            FROM ML.GENERATE_EMBEDDING(
              MODEL \`optimonk_vertex_ai.text-gecko-multilingual-embedding_v1\`,
              (SELECT id, views, descriptionHash, description as content FROM changedAndMissingEmbeddings),
              STRUCT(TRUE AS flatten_json_output)
            )`
        }
        )
        SELECT * from newEmbeddings
      ) AS newEmbeddingResult
    ON newEmbeddingResult.id = existingEmbeddingResult.id
    WHEN MATCHED THEN UPDATE SET
      existingEmbeddingResult.textEmbedding = newEmbeddingResult.textEmbedding,
      existingEmbeddingResult.descriptionHash = newEmbeddingResult.descriptionHash
    WHEN NOT MATCHED THEN INSERT
      (timestamp, id, variantId, descriptionHash, textEmbedding)
    VALUES
      (CURRENT_TIMESTAMP(), newEmbeddingResult.id, NULL, newEmbeddingResult.descriptionHash, newEmbeddingResult.textEmbedding)
    `;
};

module.exports = {
  generateTextEmbeddingsForProducts,
  generateTextEmbeddingsForExternalData,
};

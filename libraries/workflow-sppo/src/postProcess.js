const { cleanseVariableName } = require('./variables');

const DONT_MODIFY = 'dontModify';
const ADD = 'add';
const REMOVE = 'remove';

const getPostProcessSetting = (variableName, options) => {
  const cleanVariableName = cleanseVariableName(variableName);

  return options.find((option) => cleanseVariableName(option.variableName) === cleanVariableName);
};

function capitalizeFirstLetter(text) {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

function removeText(text, textToRemove) {
  const regexPattern = new RegExp(textToRemove, 'g');

  return text.replace(regexPattern, '').trim();
}

function periodAtTheEnd(text, action) {
  const formattedText = text.trim();
  const markersRegex = /[!?.;:]+$/;
  if (action === DONT_MODIFY) {
    return formattedText;
  }
  if (action === ADD) {
    // Do not add a period if already have a period
    if (markersRegex.test(formattedText)) {
      return formattedText;
    }
    return `${formattedText}.`;
  }
  if (action === REMOVE) {
    if (formattedText.indexOf('.') === -1) {
      return formattedText;
    }
    // Check if there are multiple texts
    const textParts = formattedText.split(/[.!?]/);
    if (textParts.length > 2) {
      return formattedText;
    }

    if (formattedText.endsWith('.')) {
      return formattedText.slice(0, -1);
    }
  }

  return formattedText;
}

function removeApostrophe(text) {
  return text.replaceAll(/"/g, '');
}

function postProcess(text, options) {
  let formattedText = text;
  if (options.removeApostrophe) {
    formattedText = removeApostrophe(formattedText);
  }

  if (options.removeText) {
    formattedText = removeText(formattedText, options.removeText);
  }

  if (options.capitalizeFirstLetter) {
    formattedText = capitalizeFirstLetter(formattedText);
  }

  formattedText = periodAtTheEnd(formattedText, options.periodAtTheEnd);

  return formattedText;
}

const postProcessVariables = (variables, prompts) => {
  const output = JSON.parse(JSON.stringify(variables));

  for (const prompt of prompts) {
    for (const dirtyVariableName of prompt.variableNames) {
      const variableName = cleanseVariableName(dirtyVariableName);
      const postProcessSetting = getPostProcessSetting(variableName, prompt.settings?.postProcess);

      if (variables[variableName] && postProcessSetting) {
        output[variableName] = postProcess(variables[variableName], postProcessSetting);
      }
    }
  }

  return output;
};

module.exports = {
  capitalizeFirstLetter,
  removeText,
  periodAtTheEnd,
  removeApostrophe,
  postProcess,
  getPostProcessSetting,
  postProcessVariables,
};

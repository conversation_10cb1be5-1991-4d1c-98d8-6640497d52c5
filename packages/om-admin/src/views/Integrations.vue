<template lang="pug">
div
  .brand-wrapper.re-integration
    .container-fluid
      .row.pb-5
        .col-12.align-self-center
          om-heading(h1) {{ getTitle }}
    .wrapper
      .alert-block-re-integration
        template(v-if="!isAllReIntegrated")
          alert(type="warning" :integrationType="getIntegrationType")
        template(v-else)
          success(:integrationType="getIntegrationType")
      .deprecated-integrations
        deprecated-integrations(
          :integrationType="getIntegrationType"
          :filteredIntegrations="filteredIntegrations"
          :campaignsByStatus="campaignsByStatus"
        )
      template(v-if="!needReIntegration")
        .no-need-re-integration {{ $t('reIntegration.noNeedReIntegration', { integrationType: capitalizeFirstLetter(getIntegrationType) }) }}
</template>
<script>
  import { capitalizeFirstLetter } from '@/util';
  import { mapGetters } from 'vuex';
  import { getConfigForIntegration } from '@om/integrations';
  import reIntegrationMixin from '@/components/ReIntegration/reIntegration';

  export default {
    components: {
      Alert: () => import('@/components/ReIntegration/Alerts/Alert.vue'),
      Success: () => import('@/components/ReIntegration/Alerts/Success.vue'),
      DeprecatedIntegrations: () => import('@/components/ReIntegration/DeprecatedIntegrations.vue'),
    },
    mixins: [reIntegrationMixin],
    data() {
      return {
        campaignsByStatus: {},
      };
    },
    computed: {
      ...mapGetters(['integrations']),
      getIntegrationType() {
        return this.$route.query.integrationType;
      },
      filteredIntegrations() {
        const query = this.$route.query;

        // If no query parameters, return all integrations
        if (!Object.keys(query).length) {
          return this.integrations;
        }

        return this.integrations.filter((integration) => {
          // Filter by integration type (backward compatibility)
          if (query.integrationType && integration.type !== query.integrationType) {
            return false;
          }

          // Filter by integration types (comma-separated list)
          if (query.integrationTypes) {
            const types = query.integrationTypes.split(',').map((t) => t.trim());
            if (!types.includes(integration.type)) {
              return false;
            }
          }

          return true;
        });
      },
      isDeprecated() {
        return getConfigForIntegration(this.getIntegrationType).deprecated || false;
      },
      needReIntegration() {
        return this.filteredIntegrations.length > 0 && this.isDeprecated;
      },
      getTitle() {
        if (!this.isDeprecated) {
          return this.$t(`reIntegration.noNeedReIntegration`, {
            integrationType: capitalizeFirstLetter(this.getIntegrationType),
          });
        }

        return this.$t('reIntegration.pageTitle', {
          integrationType: capitalizeFirstLetter(this.getIntegrationType),
        });
      },
    },
    async mounted() {
      const query = this.$route.query;

      this.integrationsByType = this.integrations.filter((integration) => {
        if (!Object.keys(query).length) {
          return true;
        }

        if (query.integrationType && integration.type !== query.integrationType) {
          return false;
        }
        return true;
      });

      if (this.filteredIntegrations?.length) {
        const integrationIds = this.filteredIntegrations.map((i) => i._id);
        const campaigns = await this.getCampaignsByIntegrationId(integrationIds);
        this.campaignsByStatus = campaigns;
      }
    },
    methods: {
      capitalizeFirstLetter,
    },
  };
</script>
<style lang="sass">
  @import '@/components/ReIntegration/reIntegration.sass'
</style>

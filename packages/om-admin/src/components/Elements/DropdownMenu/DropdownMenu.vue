<template lang="pug">
.ds-dropdown-menu(
  :style="{ '--item-selected-color': highlightSelected ? highlightSelectedColor : null }"
  :class="wrapperClass"
)
  Popper(
    :class="classList"
    ref="popper"
    :trigger="trigger"
    :options="options"
    @show="open = true"
    @hide="open = false"
  )
    template(slot="reference")
      .ds-dropdown-menu-trigger.d-flex.align-items-center(
        ref="reference"
        :title="tooltip"
        v-tooltip="getTooltipConfig(!open ? tooltip : null)"
      )
        slot(name="trigger")
        component.ds-dropdown-menu-trigger-icon.pointer-events-none(
          v-if="triggerIcon"
          :is="iconName"
          size="24px"
        )
    .ds-dropdown-menu-content(:style="{ width: contentWidth }")
      slot(v-if="!searchable" name="content-before")
      .ds-dropdown-menu-search.d-flex.align-items-center.justify-content-center(
        v-else
        :class="{ 'my-1 bb-1 pb-1': isGrouped }"
      )
        UilSearch.ds-dropdown-menu-search-icon(size="1.5em")
        om-input.ds-dropdown-menu-search-input.my-1(
          v-model="searchValue"
          type="text"
          :placeholder="searchPlaceholder"
          :id="id"
          small
        )
      slot(v-if="!items")
      template(v-else-if="isGrouped && !searchValue")
        .ds-dropdown-menu-group(
          v-for="(group, groupIndex) in filteredItems"
          :key="`group-${groupIndex}`"
        )
          span.ds-dropdown-menu-group-label(v-if="getGroupLabel(groupIndex)") {{ getGroupLabel(groupIndex) }}
          .ds-dropdown-menu-item.d-flex.align-items-center.justify-content-between(
            v-for="(item, index) in group"
            :key="`item-${groupIndex}${index}`"
            @click="onSelect(item)"
            :class="{ 'ds-dropdown-menu-item--selected': isSelected(item) }"
            :title="getItemLabel(item)"
          )
            slot(v-if="!iconList || !item.icon" name="item-label" v-bind="{ item, getItemLabel }")
              span {{ getItemLabel(item) }}
            component(v-else :is="item.icon" :size="iconListSize")
            UilCheck.ds-dropdown-menu-check-mark(
              v-if="showCheckMark && isSelected(item)"
              size="24px"
            )
      template(v-else)
        .ds-dropdown-menu-item.d-flex.align-items-center(
          v-for="(item, index) in filteredItems"
          :key="`item-${index}`"
          @click="onSelect(item)"
          :class="{ 'ds-dropdown-menu-item--selected': isSelected(item) }"
          :title="getItemLabel(item)"
        )
          slot(v-if="!iconList || !item.icon" name="item-label" v-bind="{ item, getItemLabel }")
            span {{ getItemLabel(item) }}
          component(v-else :is="item.icon" :size="iconListSize")
          UilCheck.ds-dropdown-menu-check-mark(
            v-if="showCheckMark && isSelected(item)"
            size="24px"
          )
      slot(name="content-after")
</template>

<script>
  import Popper from 'vue-popperjs';
  import {
    UilAngleDown,
    UilAngleUp,
    UilDirection,
    UilCheck,
    UilSearch,
  } from '@iconscout/vue-unicons';
  import { OmInput } from '@/components/Elements';

  export default {
    name: 'DropdownMenu',
    components: { Popper, UilAngleDown, UilAngleUp, UilDirection, UilCheck, UilSearch, OmInput },
    props: {
      ghost: { type: Boolean, default: false },
      trigger: { type: String, default: 'click' },
      triggerIcon: {
        type: String,
        default: null,
        validator(value) {
          return ['angle-down', 'direction', 'angle-up'].includes(value);
        },
      },
      triggerIconClose: { type: String, default: null },
      showCheckMark: { type: Boolean, default: false },
      placement: { type: String, default: 'bottom' },
      contentWidth: { type: String, default: null },
      tooltip: { type: String, default: null },
      iconList: { type: Boolean, default: false },
      iconListSize: { type: String, default: '1.25em' },
      horizontal: { type: Boolean, default: false },
      popperOptions: {
        type: Object,
        default: () => ({
          html: true,
          container: 'body',
          placement: 'bottom',
          modifiers: {
            offset: { offset: '0, 10px' },
            computeStyle: {
              gpuAcceleration: false,
            },
            preventOverflow: {
              enabled: true,
              boundariesElement: 'window',
              priority: ['left', 'right', 'top', 'bottom'],
              padding: 10,
            },
            flip: {
              enabled: true,
              behavior: ['bottom', 'top', 'right', 'right-end'],
              boundariesElement: 'window',
              padding: 10,
            },
            customPreventLeftOverflow: {
              enabled: true,
              order: 851, // Run after preventOverflow (850)
              fn(data) {
                const { popper, reference } = data.offsets;
                const viewportWidth = window.innerWidth;
                const padding = 5;

                // Only adjust if popper would overflow on the left
                if (popper.left < padding) {
                  // Calculate the reference element center
                  const referenceCenter = reference.left + reference.width / 2;
                  const popperHalfWidth = popper.width / 2;

                  // Check if we can maintain centering without left overflow
                  const idealLeft = referenceCenter - popperHalfWidth;

                  if (idealLeft >= padding) {
                    // We can center it without overflow
                    data.offsets.popper.left = idealLeft;
                  } else {
                    // We need to shift right to prevent overflow
                    data.offsets.popper.left = padding;

                    // Check if this causes right overflow
                    if (padding + popper.width > viewportWidth - padding) {
                      // If it would overflow on the right too, try to fit it as best as possible
                      const maxLeft = viewportWidth - popper.width - padding;
                      if (maxLeft > padding) {
                        data.offsets.popper.left = maxLeft;
                      }
                    }
                  }
                }

                data.styles.left = data.offsets.popper.left;
                return data;
              },
            },
            customRightEndFlip: {
              enabled: true,
              order: 852, // Run after customPreventLeftOverflow (851)
              fn(data) {
                const {
                  instance: { reference },
                } = data;
                const { popper } = data.offsets;
                const viewportHeight = window.innerHeight;
                const padding = 10;
                const placement = data.placement;

                // Only apply this modifier when placement is 'right'
                if (placement === 'right') {
                  const toolbarTop = reference
                    .closest('.tiptap-toolbar')
                    .getBoundingClientRect().top;

                  const height = popper.height + toolbarTop;

                  if (height > viewportHeight - padding) {
                    data.styles.top = 0 - popper.height - padding - (toolbarTop - viewportHeight);
                  }
                }

                return data;
              },
            },
          },
        }),
      },
      allowNull: { type: Boolean, default: false },
      searchable: { type: Boolean, default: false },
      searchPlaceholder: { type: String, default: null },
      resetSearchOnClose: { type: Boolean, default: true },
      items: { type: Array, default: null },
      groupLabels: { type: Array, default: null },
      value: { type: [String, Number, Object, Array], default: null },
      highlightSelected: { type: Boolean, default: false },
      highlightSelectedColor: { type: String, default: 'var(--brand-primary-color)' },
      id: {
        type: String,
        required: true,
      },
      wrapperClass: { type: String, default: null },
    },
    data: () => ({
      open: false,
      searchValue: null,
    }),
    computed: {
      classList() {
        return {
          'ds-dropdown-menu--ghost': this.ghost,
          'ds-dropdown-menu--open': this.open,
          'ds-dropdown-menu--grouped': this.isGrouped && !this.searchValue,
          'ds-dropdown-menu--allow-null': this.allowNull,
          'ds-dropdown-menu--show-check-mark': this.showCheckMark,
          'ds-dropdown-menu--filtered': this.searchValue,
          'ds-dropdown-menu--horizontal': this.horizontal,
          'ds-dropdown-menu--icon': this.triggerIcon,
        };
      },
      iconName() {
        const name = this.open ? this.triggerIconClose ?? this.triggerIcon : this.triggerIcon;
        return `uil-${name}`;
      },
      options() {
        return {
          ...this.popperOptions,
          placement: this.placement,
        };
      },
      isGrouped() {
        return Array.isArray(this.items?.[0]);
      },
      filteredItems() {
        if (!this.searchable || !this.searchValue) return this.items;

        if (this.isGrouped) {
          return this.items
            .map((group) =>
              group.filter((item) => {
                const regex1 = new RegExp(this.searchValue, 'i');
                const regex2 = new RegExp(this.searchValue, 'i');

                return regex1.test(this.getItemLabel(item)) || regex2.test(item?.value ?? item);
              }),
            )
            .flat();
        }

        return this.items.filter((item) => {
          const regex1 = new RegExp(this.searchValue, 'i');
          const regex2 = new RegExp(this.searchValue, 'i');

          return regex1.test(this.getItemLabel(item)) || regex2.test(item?.value ?? item);
        });
      },
    },
    watch: {
      open(open) {
        this.$emit('toolbarToggled', { open, id: this.id });

        if (!open && this.resetSearchOnClose) {
          this.searchValue = null;
        }
      },
      searchValue() {
        if (!this.open) return;
        this.updateDropdown();
      },
    },
    mounted() {
      this.$bus.$on('updatePopperPosition', this.updateDropdown);
    },
    beforeDestroy() {
      this.$bus.$off('updatePopperPosition', this.updateDropdown);
    },
    methods: {
      toggle() {
        this.$refs.popper?.doToggle?.();
      },
      showMenu() {
        this.$refs.popper?.doShow?.();
      },
      hideMenu() {
        this.$refs.popper?.doClose?.();
      },
      updateDropdown() {
        this.$refs.popper?.updatePopper?.();
      },
      onSelect(item) {
        let val = item?.value ?? item;

        if (Array.isArray(this.value)) {
          const index = this.value.indexOf(val);
          const newValue = [...this.value];

          if (index > -1) {
            newValue.splice(index, 1);
          } else {
            newValue.push(val);
          }

          val = newValue;
        } else if (this.allowNull && val === this.value) {
          val = null;
        }

        this.$emit('input', val);
        this.$emit('select', val);
        this.hideMenu();
      },
      isSelected(item) {
        const val = item?.value ?? item;
        if (Array.isArray(this.value)) {
          return this.value.includes(val);
        }

        return this.value === val;
      },
      getGroupLabel(groupIndex) {
        return this.groupLabels?.[groupIndex] ?? '';
      },
      getItemLabel(item) {
        const label = item?.label ?? item;
        return this.$te(label) ? this.$t(label) : label;
      },
      getTooltipConfig(content) {
        return {
          content,
          trigger: 'hover',
          placement: 'top',
          popover: { arrow: false },
          delay: { show: 350, hide: 0 },
          classes: 'ds-dropdown-menu-tooltip',
        };
      },
    },
  };
</script>

<style lang="sass">
  @import '@/sass/variables/_colors.sass'
  .ds-dropdown-menu
    font-size: .875rem
    display: inline-block
    &.disabled
      opacity: 0.5
      cursor: not-allowed
      .ds-dropdown-menu-trigger
        pointer-events: none
        &:hover
          background: transparent
    &-content
      background: white
      width: fit-content
      padding: 0.25rem
      border: 1px solid $om-gray-300
      border-radius: 8px
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2)
      max-height: 36.5rem
      overflow-y: auto
      z-index: 10000
    &-trigger
      padding: .25rem .1875rem
      border: 1px solid $om-gray-300
      border-radius: 4px
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1)
      cursor: pointer
      line-height: 1.6
      max-height: 2rem
      @media (hover: hover) and (pointer: fine)
        &:hover
          border-color: $om-gray-500-20
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2)
      svg
        margin-left: auto
    &--grouped
      .ds-dropdown-menu-content
        padding: 0
    &--open
      .ds-dropdown-menu-trigger
        border-color: $om-gray-500-20
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2)
        background: $om-gray-500-20
    &--ghost
      .ds-dropdown-menu-trigger
        border-color: transparent
        box-shadow: none
        @media (hover: hover) and (pointer: fine)
          &:hover
            background: $om-gray-500-20
    &--icon
      .ds-dropdown-menu-trigger
        padding-right: .25rem
        padding-left: .75rem
    .ds-dropdown-menu-search
      position: relative
      &-icon
        position: absolute
        left: 16px
      &-input
        width: calc(100% - 16px)
        input
          padding-left: 2rem !important
    &-group
      padding: .5rem
      text-align: left
      & + &
        border-top: 1px solid $om-gray-500-20
      &-label
        font-weight: bold
        padding-top: .5rem
        padding-bottom: .5rem
        padding-left: 0.3125rem
    &-item
      padding: .25rem .5rem
      border-radius: 4px
      cursor: pointer
      margin-top: .25rem
      white-space: nowrap
      &:first-child
        margin-top: 0
      &:last-child
        margin-bottom: .25rem
      @media (hover: hover) and (pointer: fine)
        &:hover
          background: $om-gray-500-20
      &--selected
        color: var(--item-selected-color)
    &-tooltip
      .tooltip-inner
        background: #4f5763 !important
      .tooltip-arrow
        display: none !important
    &--horizontal
      .ds-dropdown-menu-content
        display: flex
        flex-direction: row
        justify-content: center
        align-items: center
      .ds-dropdown-menu-item
        margin-top: 0
        & + &
          margin-left: .25rem
</style>

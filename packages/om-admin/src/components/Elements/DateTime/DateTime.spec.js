import { createLocalVue, mount } from '@vue/test-utils';
import { propTesting } from '../test-utils';
import DateTime from './index';

jest.disableAutomock();

jest.useFakeTimers();

const TEST_ID = '_id';
const localVue = createLocalVue();

const getInstance = (options = {}) => {
  const wrapper = mount(DateTime, { localVue, ...options });
  return {
    wrapper,
    subject: wrapper.vm,
  };
};

describe('Components::DateTime - default props', () => {
  const cases = [
    ['label', ''],
    ['locale', 'en'],
    ['errorMessage', ''],
    ['error', false],
    ['disabled', false],
    ['mode', 'single'],
    ['size', 'normal'],
    ['designSystem', true],
    ['minDate', null],
    ['maxDate', null],
  ];

  test.each(cases)('DateTime properties: %p should be %p', (property, expected) =>
    propTesting(
      { component: DateTime, expect },
      { property, expected },
      { localVue, propsData: { id: TEST_ID } },
    ),
  );
});

describe('Components::DateTime - computed properties', () => {
  const cases = [
    ['datetime-locale-hu', true, { locale: 'hu' }],
    ['datetime-locale-en', true, { locale: 'en' }],
    ['datetime-locale-en', true, {}],
    ['datetime-with-minDate', 'today', { minDate: 'today' }],
    ['datetime-with-maxDate', 'today', { maxDate: 'today' }],
    ['datetime-single', true, {}],
    ['datetime-single', true, { mode: 'single' }],
    ['datetime-range', true, { mode: 'range' }],
    ['datetime-showClear', true, { showClear: true }],
    ['datetime-error', true, { error: true }],
    ['datetime-disabled', true, { disabled: true }],
    ['design-system', true, {}],
    ['design-system', false, { designSystem: false }],
    ['design-system', true, { designSystem: true }],
  ];
  test.each(cases)('DateTime Classes %p should be %p', (property, expected, propsData) =>
    propTesting(
      { component: DateTime, expect, subjectProperty: 'classes' },
      { property, expected },
      { propsData: { ...propsData, id: TEST_ID }, localVue },
    ),
  );
});

describe('Components::DateTime - single datetime', () => {
  test('initFlatpickr is called', () => {
    const { wrapper } = getInstance({
      propsData: { id: TEST_ID, value: '02-20-2022', locale: 'en', mode: 'single' },
    });
    const spy = jest.spyOn(wrapper.vm, 'initFlatpickr');
    jest.runAllTimers();
    expect(spy).toHaveBeenCalled();
  });
});

describe('Components::DateTime - single datetime', () => {
  const { wrapper, subject } = getInstance({
    propsData: { id: TEST_ID, value: '02-20-2022', locale: 'en', mode: 'single' },
  });

  test('flatckr is ready', async () => {
    const calendar = wrapper.find('.flatpickr');
    const readyClass = 'flatpickr-input';
    calendar.trigger('click');
    jest.runAllTimers();
    await subject.$nextTick();
    expect(calendar.element.classList.contains(readyClass)).toBe(true);
  });
});

describe('Components::DateTime - check value is emitted correctly', () => {
  const value = '2022-02-01'; // FORMAT: YYYY-MM-DD
  const { wrapper, subject } = getInstance({
    propsData: { id: TEST_ID, locale: 'hu', mode: 'single' },
  });

  test('hu single mode value is emitted', async () => {
    await wrapper.setProps({ value });
    expect(subject.date).toBe(value);
  });
});

describe('Components::DateTime - check value is emitted correctly', () => {
  const value = '01-02-2022'; // FORMAT: DD-MM-YYYY
  const { wrapper, subject } = getInstance({
    propsData: { id: TEST_ID, locale: 'en', mode: 'single' },
  });

  test('en single mode value is emitted', async () => {
    await wrapper.setProps({ value });
    expect(subject.date).toBe(value);
  });
});

describe('Components::DateTime - check value is emitted correctly', () => {
  const value = ['2022-02-20', '2022-02-24']; // FORMAT: YYYY-MM-DD
  const { wrapper, subject } = getInstance({
    propsData: { id: TEST_ID, locale: 'hu', mode: 'range' },
  });

  test('hu range mode value is emitted', async () => {
    await wrapper.setProps({ value });
    expect(subject.date).toBe(value);
  });
});

describe('Components::DateTime - check value is emitted correctly', () => {
  const value = ['02-20-2022', '02-24-2022']; // FORMAT: DD-MM-YYYY
  const { wrapper, subject } = getInstance({
    propsData: { id: TEST_ID, locale: 'en', mode: 'range' },
  });

  test('en range mode value is emitted', async () => {
    await wrapper.setProps({ value });
    expect(subject.date).toBe(value);
  });
});

describe('Components::DateTime - check number value is emitted correctly', () => {
  const value = 1676960144000;
  const { wrapper, subject } = getInstance({
    propsData: { id: TEST_ID, locale: 'en', mode: 'single' },
  });

  test('en single mode value is emitted', async () => {
    await wrapper.setProps({ value });
    expect(subject.date).toBe(value);
  });
});

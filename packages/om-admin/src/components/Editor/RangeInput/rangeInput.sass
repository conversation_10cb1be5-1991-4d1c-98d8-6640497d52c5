@import '../../../sass/variables/_colors.sass'
@import '../../../editor/sass/shared/_vars.sass'

.ds-range-input
  &-label
    display: flex
    align-items: center
    font-size: 0.75rem
    color: $om-dark-grey-3
    line-height: 1rem
    margin-bottom: unset
  &-content
    &-row
      width: 100%
      min-height: 2rem
      display: flex
      flex-direction: column
  #ds-range-input-wrapper
    position: relative
    .ds-range-input-low-filler
      position: absolute
      top: 6px
      height: 4px
      background: $om-orange
      border-radius: 999px
      pointer-events: none
      z-index: 2
    #ds-range-input-slider
      width: 100%
      height: 4px
      -webkit-appearance: none
      appearance: none
      display: flex
      align-items: center
      position: relative
      background: transparent
      overflow: hidden
      height: 1rem
      // Appearance of the thumb
      &::-webkit-slider-thumb
        -webkit-appearance: none
        height: 1rem
        width: 1rem
        border: 4px solid $om-orange
        border-radius: 100%
        background: white
        margin-top: -6px
        z-index: 3
        position: relative
      &::-moz-range-thumb
        -webkit-appearance: none
        height: 1rem
        width: 1rem
        border: 4px solid $om-orange
        border-radius: 100%
        background: white
      &::-ms-thumb
        -webkit-appearance: none
        height: 1rem
        width: 1rem
        border: 4px solid $om-orange
        border-radius: 100%
        background: white
      // Appearance of the track
      &::-webkit-slider-runnable-track
        -webkit-appearance: none
        appearance: none
        background: $om-light-grey-2
        border-radius: 4px
        height: 4px

  &-value-indicator
    gap: 0.15rem
    height: 2rem
    border-radius: 4px
    padding: 0.25rem 0.4375rem 0.25rem 0.2rem
    display: flex
    flex-direction: row
    border: 1px solid $om-light-grey2
    background: #fff
    font-size: .8125rem
    justify-content: flex-start
    align-items: baseline
    margin-top: auto
    margin-bottom: auto
    &-invalid
      border: 1px solid $validation-failed-red
      color: #696d72
  &-value-style
    font-size: 0.875rem
    color: $om-dark-grey-3
    text-align: right
    border: 0
    background-color: transparent
    width: 2.2rem
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button
      -webkit-appearance: none
      margin: 0

  &-unit
    display: flex
    align-items: center
    font-size: 0.875rem
    min-width: 1rem
    color: $om-dark-grey-3

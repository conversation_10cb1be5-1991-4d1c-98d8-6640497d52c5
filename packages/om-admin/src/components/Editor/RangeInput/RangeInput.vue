<template lang="pug">
.ds-range-input
  .ds-range-input-content
    PaneLayout(layout="col" gridLayout="three-fourth")
      template(#left)
        .ds-range-input-content-row.pr-2.justify-content-center
          label.ds-range-input-label(v-if="label") {{ label }}&nbsp;
            DeviceSelector(v-if="editMobile" :hasViewText="false")
          #ds-range-input-wrapper(:class="{ 'mt-1': label }")
            .ds-range-input-low-filler(:style="{ width: calculatedWidth }")
            input#ds-range-input-slider(
              type="range"
              :min="min"
              :max="max"
              :step="step"
              v-model="valueMod"
            )
      template(#right)
        span.ds-range-input-value-indicator(
          :class="{ focus: focus, 'ds-range-input-value-indicator-invalid': isInvalid }"
        )
          input.ds-range-input-value-style(
            :name="inputName"
            type="number"
            v-model="valueMod"
            :min="limitInput ? min : null"
            :max="limitInput ? max : null"
            :step="limitInput ? step : null"
            @focus="focus = true"
            @blur="focus = false"
            :placeholder="placeholder"
            @change="$emit('input', value)"
            :autocomplete="autocomplete"
          )
          span.ds-range-input-unit(v-if="value !== null || fixUnit") {{ unit }}
</template>

<script>
  import designSystemMixin from '@/components/Elements/mixins/designSystem';

  export default {
    name: 'RangeInput',
    components: {
      PaneLayout: () => import('../PaneLayout.vue'),
      DeviceSelector: () => import('@/editor/components/sidebar/components/DeviceSelector.vue'),
    },
    mixins: [designSystemMixin],
    props: {
      label: { type: String, required: true },
      min: { type: Number, default: 0 },
      max: { type: Number, default: 200 },
      step: { type: Number, default: 5 },
      value: { type: Number },
      unit: { type: String, default: 'px' },
      editMobile: { type: Boolean, default: false },
      isInvalid: { type: Boolean, default: false },
      fixUnit: { type: Boolean, default: false },
      placeholder: { type: String, default: null },
      limitInput: { type: Boolean, default: false },
      inputName: { type: String, default: null },
      autocomplete: { type: String, default: null },
    },
    data() {
      return {
        focus: false,
      };
    },

    computed: {
      calculatedWidth() {
        const value = ((this.valueMod - this.min) / (this.max - this.min)) * 100;
        const percentage = value < 0 ? 0 : value > 100 ? 100 : value;
        return `${percentage}%`;
      },
      valueMod: {
        get() {
          return this.value;
        },
        set(v) {
          if (this.limitInput) {
            if (this.min && this.min > v) v = this.min;
            if (this.max && this.max < v) v = this.max;
          }
          this.$emit('input', v);
        },
      },
    },

    watch: {
      focus(value) {
        this.$emit('inputFocus', value);
      },
    },
  };
</script>

<style lang="sass">
  @import 'rangeInput.sass'
</style>

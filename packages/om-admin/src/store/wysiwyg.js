function isFrameStore() {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
}

function commitParentStore(mutation, value) {
  window?.parent?.top?.om?.store?.commit?.(`wysiwyg/${mutation}`, value);
}

export const SET_ACTIVE_ELEMENT = 'SET_ACTIVE_ELEMENT';
export const SET_VISIBLE = 'SET_VISIBLE';
export const SET_MARGIN_VISUALIZER_ACTIVE = 'SET_MARGIN_VISUALIZER_ACTIVE';
export const SET_ELEMENT_RESIZE_ACTIVE = 'SET_ELEMENT_RESIZE_ACTIVE';
export const SET_POSITION = 'SET_POSITION';
export const SET_IS_TIPTAP = 'SET_IS_TIPTAP';

export default {
  namespaced: true,
  state: {
    activeElement: null,
    visible: false,
    marginVisualizerActive: false,
    elementResizeActive: false,
    position: null,
  },
  getters: {
    isVisualizersActive(state) {
      return state.marginVisualizerActive || state.elementResizeActive;
    },
    hasActiveElement(state) {
      return state.activeElement;
    },
    isVisible(state, getters) {
      return state.visible && !getters.isVisualizersActive && state.activeElement;
    },
    isTiptap() {
      const type = window.parent.top.om.store.state.selectedElement?.type
        ?.replace?.('Om', '')
        ?.toUpperCase?.();
      return [`EDITOR_TIPTAP_${type}`, 'EDITOR_TIPTAP_ALL'].some((feature) =>
        window.parent.top.om.store.getters.hasAccountFeature(feature),
      );
    },
  },
  mutations: {
    [SET_ACTIVE_ELEMENT](state, element) {
      state.activeElement = element;
      if (isFrameStore()) commitParentStore(SET_ACTIVE_ELEMENT, element);
    },
    [SET_VISIBLE](state, visible) {
      state.visible = visible;
      if (isFrameStore()) commitParentStore(SET_VISIBLE, visible);
    },
    [SET_MARGIN_VISUALIZER_ACTIVE](state, active) {
      state.marginVisualizerActive = active;
      if (isFrameStore()) commitParentStore(SET_MARGIN_VISUALIZER_ACTIVE, active);
    },
    [SET_ELEMENT_RESIZE_ACTIVE](state, active) {
      state.elementResizeActive = active;
      if (isFrameStore()) commitParentStore(SET_ELEMENT_RESIZE_ACTIVE, active);
    },
    [SET_POSITION](state, position) {
      state.position = position;
      if (isFrameStore()) commitParentStore(SET_POSITION, position);
    },
  },
  actions: {
    hideWysiwyg({ state, getters, commit }) {
      commit(SET_VISIBLE, false);
      if (getters.isVisualizersActive && state.activeElement && !getters.isTiptap) {
        commit(
          'setStateAttr',
          { attr: 'quillToolbarInfo', value: { show: false } },
          { root: true },
        );
      }
    },
    showWysiwyg({ getters, state, commit, dispatch }) {
      commit(SET_VISIBLE, true);
      if (state.activeElement && !getters.isVisualizersActive && !getters.isTiptap) {
        // this._vm.$bus.$emit('deselectAll');
        const uid = state.activeElement;
        if (isFrameStore()) {
          this._vm.$nextTick(() => {
            dispatch('selectElement', { uid }, { root: true });
          });
        }
      }
    },
    updateWysiwyg({ commit }, { uid = null, visible = false } = {}) {
      commit(SET_ACTIVE_ELEMENT, uid);
      commit(SET_VISIBLE, visible);
    },
    refreshPosition({ commit }, { top = 0, left = 0, width = 0, height = 0 } = {}) {
      commit(SET_POSITION, { top, left, width, height });
    },
  },
};

$nav-link: #272727

.brand-custom-navbar
  height: 5.625rem
  @media screen and (max-width: 768px)
    height: 68px
    padding: 0 24px
    box-shadow: none
    > div
      padding: 0
      border-bottom: 1px solid #EBEDEF
    .navbar-brand .logo
      max-height: 32px
  background-color: white
  padding: 0 1rem
  transition: .3s
  z-index: 200001
  border-bottom: 1px solid #EEEFF1

  .navbar-brand
    margin-right: 1.25rem
    padding: 0
    flex-grow: 0
    .logo
      min-width: auto
      max-width: 130px
      max-height: 30px
  .account-navigation
    .account-name
      font-size: 1.375rem
      font-weight: 800
      text-transform: uppercase
      max-width: 200px
      white-space: nowrap
      overflow: hidden
      text-overflow: ellipsis
    .account-navigation-back
      font-size: .75rem
      font-weight: 500
      display: flex
      align-items: center
  &-slim
    height: 3.5rem

    .navbar-brand .logo
      max-height: 25px

  .navbar-nav
    height: 100%
    padding: 0
    flex: 1
    border-left: 1px solid #EEEFF1
    &.agency-navbar-nav
      .nav-item
        margin: 0 .25rem
    .nav-item
      display: flex
      align-items: center
      margin: 0 .25rem
      border-bottom: solid 3px transparent
      border-top: solid 3px transparent
      &.router-link-active
        border-bottom: solid 3px var(--brand-primary-color)
        .nav-link
          color: #bfc5d1
          &:hover
            color: var(--brand-primary-color)
    .nav-link
      font-size: .9375rem
      font-weight: 500
      text-transform: uppercase
      color: $nav-link
      &:hover
        color: var(--brand-primary-color)
        .step
          background: var(--brand-primary-color)

  .profile
    display: flex
    align-items: center
    margin-left: auto
    position: relative
    justify-content: flex-end
    flex-grow: 1
    max-width: 195px
    .profile-notification-bell
      position: relative
      font-size: 1.25rem
      cursor: pointer
      .fa
        font-size: 1.3215rem
        color: #bfc5d1
      .profile-notification-counter
        display: flex
        justify-content: center
        align-items: center
        position: absolute
        top: -.3125rem
        right: -.5rem
        min-width: 1.125rem
        height: 1.125rem
        border-radius: 100%
        color: white
        background-color: var(--brand-primary-color)
        font-size: .75rem
        font-weight: 500
        padding: 0 .125rem
    .profile-img-holder
      position: relative
      height: 40px
      width: 40px
      background-color: #D8D8D8
      border-radius: 100%

    .profile-info
      display: flex
      flex-direction: row
      align-items: center
      cursor: pointer
      &-wrapper
        padding: 0 15px
        flex: 1
      .profile-owner
        margin-right: .625rem
        padding-right: .625rem
        border-right: 1px solid #e9eff4
        text-align: right
        .profile-owner-name
          font-weight: 700
          color: #56575c
        .profile-owner-email
          font-size: .6875rem
          color: #c1c1c1
          line-height: 1.2
          max-width: 121px
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis
    .notification-dropdown
      background-color: $om-dark-grey-2
      top: 20px !important
      padding: 1.25rem
      padding-right: .9375rem
      width: 28.125rem

      .profile-notifications
        max-height: 338px
        overflow-y: scroll
      .profile-notification
        margin-bottom: 1.25rem
        padding-right: 1.25rem
        &-read
          opacity: 0.4
        &:last-child
          margin-bottom: 0
        .profile-notification-title
          flex: 2
          color: white
          text-overflow: ellipsis
          white-space: nowrap
          overflow: hidden
          font-weight: 500
          margin-bottom: 8px
        .profile-notification-date
          flex: 1
          text-align: right
          font-size: 11px
          font-weight: 500
          color: #c2c2c2
        .profile-notification-lead
          color: $om-light-grey-2
          font-size: 12px
          font-weight: 500
          max-height: 55px
          overflow-y: hidden
        .profile-notification-mark-as-read
          text-align: right
          font-size: 11px
          font-weight: 500
          color: var(--brand-primary-color)
          cursor: pointer

          &:hover
            text-decoration: underline
            opacity: 0.8
      .popper__arrow
        border-color: transparent transparent $om-dark-grey-2 transparent !important

    .profile-dropdown
      width: 15.625rem
      top: 0.8125rem !important
      background-color: $brand-popper-bg
      color: white
      padding: 1.0625rem 1.25rem !important
      padding-bottom: 0.5rem
      left: -14px !important
      margin-top: 8px

      .profile-plan-text
        text-transform: uppercase
        font-size: 0.6875rem
      .profile-plan
        font-weight: 500
        font-size: 0.875rem
        text-transform: uppercase
      .profile-dropdown-link
        display: block
        position: relative
        padding: 0.3125rem 0
        font-size: 0.8125rem
        font-weight: 500
        line-height: 1.55

        &::after
          position: absolute
          right: 0
          top: calc(50% - 0.25rem)
          content: ''
          border: solid var(--brand-primary-color)
          border-width: 0 0.0625rem 0.0625rem 0
          display: inline-block
          padding: 0.1875rem
          transform: rotate(-45deg)

        &-small
          font-size: 0.6875rem
          padding: 0.125rem 0
        &-red
          color: #e7654d

          &::after
            content: none

      .profile-separator
        margin: 0.5625rem -1.25rem
        border-color: #3f3f3f

      .popper__arrow
        border-color: transparent transparent $brand-popper-bg transparent !important

.arrow-down
  border: solid black
  border-width: 0 0.125rem 0.125rem 0
  width: 0.5625rem
  height: 0.5625rem
  display: inline-block
  transform: rotate(45deg)
  margin: 0 .25rem
/* scrollbar x-style */
.profile-notifications::-webkit-scrollbar
  height: 3px
  width: 6px
  background-color: transparent

.profile-notifications::-webkit-scrollbar-thumb
  background-color: var(--brand-primary-color)
  border-radius: 10px

.profile-notifications::-webkit-scrollbar-track
  -webkit-box-shadow: inset 0 0 3px rgba(0,0,0,0.3)
  border-radius: 10px
  background-color: rgba(0,0,0,0.3)
/* /scrollbar x-style */

/* agency account */

.agency-account-wrapper
  flex-grow: 0.5
  margin-right: 10px

/* /agency account */

/* insert code wrap */

.brand-insert-code-wrapper
  padding: 0 6px
  display: flex
  align-items: center
  justify-content: center
  height: 100%
  flex-grow: 0

/* insert code wrap */

/* mobile nav */

.navbar-nav-mobile
  position: absolute
  top: 100%
  left: 0
  padding: 0
  background-color: #231f20
  border-radius: 3px
  display: none
  max-width: 130px
  box-shadow: rgb(58, 58, 58) 0 0 6px 0
  &:before
    content: ''
    position: absolute
    border-width: 0 5px 5px 5px
    width: 0
    height: 0
    border-style: solid
    border-color: transparent transparent #231f20 transparent !important
    top: -5px
    left: 7px
  &-wrapper
    position: relative
    height: 100%
    display: flex
    align-items: center
    margin: 0 5px
    flex-grow: 15
    i
      font-size: 1.5rem
  &-circle
    width: 35px
    height: 35px
    display: flex
    flex-wrap: wrap
    justify-content: center
    align-items: center
    border: 1px solid var(--brand-primary-color)
    border-radius: 3px
    color: var(--brand-primary-color)
    i
      font-size: 21px
  &.menu-open
    display: flex
    flex-wrap: wrap
  .nav-item
    list-style: none
    flex: 1
    padding: 1.0625rem 2.5rem
    .nav-link
      padding: 0
      &:hover
        text-decoration: underline
    & + .nav-item
      border-top: 1px solid #3f3f3f

.brand-mobile-navbar
  opacity: 0
  border-bottom: 0

  @include media-breakpoint-down(md)
    opacity: 1
/* /mobile nav */

/* responsive style */

@media screen and (min-width: 768px)
  .brand-custom-navbar .profile .profile-dropdown
    margin-top: 11px
  .brand-custom-navbar .profile .profile-dropdown
    left: -35px !important
@media screen and (min-width: 992px)
  .brand-custom-navbar
    padding: 0
  .navbar-expand-lg .navbar-nav .nav-link
    padding-right: .1875rem
    padding-left: .1875rem
  .brand-custom-navbar .profile .profile-dropdown
    margin-top: 13px
@media screen and (min-width: 1400px)
  .brand-insert-code-wrapper
    padding: 0 9px
  .brand-custom-navbar .navbar-nav
    padding: 0 2.1875rem
  .brand-custom-navbar .navbar-nav .nav-item
    margin: 0 1.125rem
  .brand-custom-navbar .navbar-nav.agency-navbar-nav .nav-item
    margin: 0 0.75rem
  .brand-custom-navbar .profile .profile-dropdown
    left: -41px !important


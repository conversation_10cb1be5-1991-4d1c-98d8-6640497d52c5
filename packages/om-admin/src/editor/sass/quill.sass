@mixin smart-tag-style-apply
  background: rgba(237, 90, 41, 0.1) !important
  border: none !important
  padding: 4px 8px !important
  text-align: center !important
  text-decoration: none !important
  display: inline-block !important
  border: 1px dashed #ED5A29 !important
  border-radius: 4px !important
@mixin smart-tag-style-remove
  background: transparent !important
  border: none !important
  padding: unset !important
  text-align: unset !important
  text-decoration: none !important
  display: inline-block !important
  border: unset !important
  border-radius: unset !important

// Hack to not render links inner font-size multiplier
.om-tiptap-link
  span
    font-size: 1em !important

.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow .ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected
  color: $om-orange !important
  .ql-stroke
    stroke: $om-orange !important
  .ql-fill
    fill: $om-orange !important
.ql-separator-horizontal
  width: 100%
  height: 1px
  background: #4c4e52
  margin: 4px 0
.ql-separator-vertical
  background: #4c4e52
  width: 1px
  height: 20px
  margin: 2px 5px 0
.ql-toolbar
  position: absolute
  top: 0
  left: 0
  right: 0
  width: 35.8rem
  min-width: 510px
  background: #323438 !important
  z-index: 3
  color: #fff
  [lang="hu"] &
    width: 37.75rem
  &.ql-snow
    padding: 5px 10px !important
    border-radius: 3px
    border: 1px solid #323438 !important
    font-family: 'Roboto', sans-serif !important
    .ql-fill
      fill: #fff !important
    .ql-stroke:not(.ql-color-label)
      stroke: #fff !important
    .ql-clean
      .ql-stroke:nth-child(3),
      .ql-stroke:nth-child(4)
        stroke: red !important
    .ql-picker-options
      background: #323438 !important
      .ql-picker-item
        color: #fff !important
        &:hover
          background: #4c4e52
      .ql-picker-item:first-child
        background: #4c4e52 !important
        font-weight: 700
        border-bottom: 1px solid #4c4e52 !important
        pointer-events: none
    &.ql-toolbar button,
    .ql-toolbar button
      height: 27px !important
      width: 27px !important
      border-radius: 3px
      padding: 5px !important
    .ql-icon-picker
      .ql-picker-item
        margin: 2px 0
    .ql-color-picker
      width: 26px !important
      height: 22px !important
      .ql-picker-label
        padding-left: 3px !important
        padding-top: 4px !important
        padding-bottom: 5px !important
    .ql-picker
      font-size: 12px !important
      height: 22px !important
      line-height: 22px
      float: none
      border-radius: 3px
      &.ql-align,
      &.ql-color-picker
        height: 27px !important
        line-height: 27px
      &.ql-align
        .ql-picker-label
          vertical-align: middle
      &.ql-expanded .ql-picker-options
        border-color: #4c4e52 !important
        padding: 0 2px !important
      &.ql-font
        width: 120px !important
        .ql-picker-label:before
          width: 7rem
          overflow: hidden
          text-overflow: ellipsis
          height: 1.875em
          padding-right: .5rem
      &.ql-size
        width: 60px !important
      &.ql-lineheight
        width: 45px !important
      &.ql-weight
        width: 85px !important
      &.ql-smart-tags
        width: 125px !important
        [lang="hu"] &
          width: 141px !important
          @media screen and (max-width: 576px)
            width: 130px !important
        .ql-picker-options
          min-width: 240px
          left: -22px
          @media screen and (max-width: 576px)
            left: auto
            right: 0
      &:not(.ql-icon-picker)
        .ql-picker-options
          max-height: 16rem
          overflow-y: scroll
          padding: 0 !important
          .ql-picker-item
            padding: 5px 8px !important
            &:first-child
              background: #f7f8f9
              border-bottom: 1px solid #edeff1
              &:hover
                cursor: auto
                color: #444 !important
      &-wrapper
        display: flex
        justify-content: flex-start
        align-items: center
        margin: 1px 0
        &.ql-picker-font
          label
            padding-left: 0
            &:after
              display: none
        label
          padding: 0 2px 0 15px
          font-size: 12px
          vertical-align: middle
          height: 20px
          line-height: 22px
          position: relative
          margin-left: 4px
          &:after
            position: absolute
            content: ""
            width: 1px
            height: 20px
            background: #4b4c50
            top: 1px
            left: 0
      &-options
        line-height: 15px
      &-label
        padding-left: 5px !important
        color: #fff !important
      &.ql-expanded
        .ql-picker-label
          border-color: transparent !important
    .ql-formats
      display: flex
      vertical-align: middle
      justify-content: flex-start
      align-items: center
      flex-wrap: wrap
      margin-right: 0 !important
    & .ql-picker,
    &.ql-toolbar button,
    & .ql-toolbar button
      &:hover
        background: #4c4e52
      svg
        fill: #fff !important

.ql-picker-svg
  width: 18px
  height: 18px

.ql-container
  font-size: inherit !important
  &.ql-snow
    border: 0

.ql-snow .ql-tooltip
    background-color: #323438 !important
    border: 0 !important
    box-shadow: none !important
    color: #fff !important
    padding: 6px 13px !important
    white-space: nowrap !important
    border-radius: 3px !important
    font-size: 14px !important
    min-width: 20rem
    max-width: 23rem
    display: flex
    flex-wrap: wrap
    @media screen and (max-width: 767px)
      min-width: 23.6875rem
    &.ql-editing,
    &.ql-flip
      border: 0 !important
      box-shadow: none !important
.ql-snow .ql-hidden
    display: none !important
.ql-snow
  a.ql-preview,
  a.ql-action,
  a.ql-remove
    color: $om-orange !important
    display: flex !important
    align-items: center !important

.ql-snow
  a.ql-preview
    padding-right: 0.625rem
    padding-bottom: 0.3125rem
    max-width: 260px
    flex: 0 0 100%

.ql-snow .ql-tooltip input[type=text]
  border-radius: 3px
  border: 0 !important
  background: #edeff1
  &::placeholder
    color: #aaaaaa
    opacity: 1

  &:-ms-input-placeholder
    color: #aaaaaa

  &::-ms-input-placeholder
    color: #aaaaaa

.ql-action
  &:hover
    opacity: .7 !important

.ql-editor
  width: 100% !important
  line-height: inherit !important
  padding: 0 !important
  overflow-y: inherit !important
  white-space: normal !important
  height: auto !important
  .smart-tag
    .smart-tag-content
      @include smart-tag-style-apply
      span
        @include smart-tag-style-remove
    span
      @include smart-tag-style-remove
      span
        @include smart-tag-style-apply
    .smart-tag-migrated
      @include smart-tag-style-apply

html:lang(hu)
  .ql-snow .ql-tooltip[data-mode=link]:before
    content: 'Kérem a linket:'
  .ql-snow .ql-tooltip.ql-editing a.ql-action:after
    content: 'Mentés'

/* scrollbar x-style */
.ql-picker-options::-webkit-scrollbar
  width: 8px
  background-color: #323438

.ql-picker-options::-webkit-scrollbar-thumb
  background-color: $om-orange
  border-radius: 10px

.ql-picker-options::-webkit-scrollbar-track
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3)
  border-radius: 10px
  background-color: #323438
/* /scrollbar x-style */

/* quill font weight set */
.ql-weight
  .ql-picker-item,
  .ql-picker-label
    font-family: 'Raleway', sans-serif
    &[data-value="300"]
      font-weight: 300
    &[data-value="400"]
      font-weight: 400
    &[data-value="500"]
      font-weight: 500
    &[data-value="600"]
      font-weight: 600
    &[data-value="700"]
      font-weight: 700
    &[data-value="800"]
      font-weight: 800
    &[data-value="900"]
      font-weight: 900

/* quill font weight set */

.ql-btn
  border-radius: 3px
  background-color: #4c4e52 !important
  display: flex
  justify-content: center
  align-items: center
  font-size: 1em
  &-add
    color: white
    &:hover
      color: #ed5a29

.ql-snow.ql-toolbar
  button.ql-btn
    padding: 0 !important

.ql-picker-font-search
  display: block
  background: inherit
  padding: 4px
  position: sticky
  top: 0px
  input
    display: block
    background: rgba(0,0,0,0.4)
    appearance: none
    border: none
    border-radius: 4px
    color: #fff
    padding: 4px
    font-size: 12px
    outline: 0
  &-empty
    pointer-events: none
    padding: 5px 8px
    color: #fff
    font-weight: normal
    display: block
    opacity: 0.55

<template lang="pug">
DropdownMenu#line-height-toolbar-menu(
  ghost
  @toolbarToggled="handleToolbarOpen"
  :tooltip="$t('tiptap.lineHeight')"
)
  template(#trigger)
    UilLineSpacing(size="24px")
  .p-2
    RangeInput(
      :label="$t('tiptap.lineHeight')"
      unit=""
      v-model.number="value"
      @inputFocus="isInputFocused = $event"
      :min="0.5"
      :max="6"
      :step="0.01"
      limitInput
      inputName="line-height-input"
      autocomplete="new-line-height-value"
    )
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import RangeInput from '@/components/Editor/RangeInput/RangeInput.vue';
  import { UilLineSpacing } from '@iconscout/vue-unicons';
  import commonMixin from './mixins/common';

  export default {
    name: 'LineHeight',
    components: { DropdownMenu, UilLineSpacing, RangeInput },
    mixins: [commonMixin],
    data: () => ({
      selected: null,
      isInputFocused: false,
    }),
    computed: {
      value: {
        get() {
          return Number(this.selected);
        },
        set(value) {
          this.$emit('editorCommand', {
            command: 'setLineHeight',
            value: [value, this.isInputFocused],
          });
          this.$nextTick(() => {
            this.updateSelected();
          });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'lineHeightChange',
              settingName: 'lineHeight',
              settingValue: value,
            },
          });
        },
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        this.updateSelected();
      },
      updateSelected() {
        const { lineHeight = null } = window.top._editor?.getAttributes?.('paragraph') ?? {};
        const { lineHeight: styleLineHeight = null } =
          window.top._editor?.getAttributes?.('textStyle') ?? {};
        this.selected = Number((lineHeight || styleLineHeight) ?? '1');
      },
    },
  };
</script>

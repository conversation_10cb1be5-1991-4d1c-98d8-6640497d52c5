<template lang="pug">
DropdownMenu#font-family-toolbar-menu(
  showCheckMark
  :items="options"
  :groupLabels="groupLabels"
  ghost
  triggerIcon="angle-down"
  triggerIconClose="angle-up"
  placement="right"
  contentWidth="285px"
  wrapperClass="font-family"
  :tooltip="$t('tiptap.fontFamily')"
  searchable
  v-model="value"
)
  template(#trigger)
    .font-family-toolbar-menu-label(:style="{ 'font-family': labelFontFamily }") {{ label }}
  template(#item-label="{ getItemLabel, item }")
    span(:style="{ 'font-family': `'${itemLabelFontFamily(item.value)}'` }") {{ getItemLabel(item) }}
  template(#content-after)
    .dropdown-footer
      om-link(primary withIconLeft @click="$modal.show('custom-font-upload-v2')" )
        template(slot="left-icon")
          UilPlusCircle.mr-2(size="24px")
        span {{ $t('tiptap.addNewFont') }}
</template>

<script>
  import { mapState, mapGetters } from 'vuex';
  import { UilPlusCircle } from '@iconscout/vue-unicons';
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import commonMixin from './mixins/common';
  import { getStyles } from '../helpers/getStyles';

  export default {
    name: 'FontFamily',
    components: { UilPlusCircle, DropdownMenu },
    mixins: [commonMixin],
    data: () => ({
      selected: null,
      theme: null,
      recent: null,
      other: null,
    }),
    computed: {
      ...mapState(['selectedElement', 'fonts']),
      ...mapGetters(['isThemeKit', 'getCustomThemeKitValues', 'installedFonts']),

      currentFont() {
        return this.installedFonts.find(({ key }) => key === this.selected);
      },

      label() {
        const font =
          this.theme?.find?.(({ value }) => value === this.selected) ??
          this.recent?.find?.(({ value }) => value === this.selected) ??
          this.other?.find?.(({ value }) => value === this.selected);

        return font?.label ?? '';
      },
      labelFontFamily() {
        const themeFont = this.selected?.match?.(/om-font-(\d)/)?.[1] ?? null;

        if (themeFont) {
          const themeFontIndex = Number(themeFont) - 1;
          const themeFontKey = this.getCustomThemeKitValues.fonts[themeFontIndex];
          return this.installedFonts.find(({ key }) => key === themeFontKey)?.family;
        }

        const { family } = this.installedFonts.find(({ key }) => key === this.selected) ?? {};

        return family;
      },
      options() {
        const options = [];

        if (this.isThemeKit && this.theme) {
          options.push(this.theme);
        }

        options.push(this.other ?? []);

        return options;
      },
      groupLabels() {
        const labels = [];

        if (this.isThemeKit && this.theme) {
          labels.push(this.$t('tiptap.themeFonts'));
        }

        labels.push(this.$t('tiptap.allFonts'));

        return labels;
      },
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          this.$emit('editorCommand', { command: 'setFontFamily', value });
          this.$nextTick(() => {
            this.updateSelected();
          });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'fontFamilyChange',
              settingName: 'fontFamily',
              settingValue: value,
            },
          });
        },
      },
      oldCampaignFontFamily() {
        const element = document
          .querySelector('#workspaceFrame')
          ?.contentWindow?.document?.getElementById?.(this.selectedElement.uid);
        const fontFamilyRaw = getComputedStyle(element)?.fontFamily?.replace?.(/"/g, '');

        return this.searchFont(fontFamilyRaw) ?? 'open-sans';
      },
      fallback() {
        return this.isThemeKit ? 'om-font-1' : this.oldCampaignFontFamily;
      },
    },
    watch: {
      labelFontFamily: {
        handler() {
          this.refreshFonts();
        },
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        this.refreshFonts();
        this.updateSelected();
      },

      refreshFonts() {
        this.theme = this.findThemeFonts();
        this.other = this.findOtherFonts();
      },

      findThemeFonts() {
        if (!this.isThemeKit) {
          this.themeFonts = null;
          return;
        }

        return this.getCustomThemeKitValues.fonts
          .map((themeFontKey, index) => {
            const font = this.installedFonts.find((font) => font.key === themeFontKey);
            const fontLabel = index ? 'Secondary font' : 'Primary font';
            return font
              ? {
                  label: `${font.family} (${fontLabel})`,
                  value: `om-font-${index + 1}`,
                }
              : null;
          })
          .filter(Boolean);
      },

      findOtherFonts() {
        return this.installedFonts
          .filter(({ key }) => !this.theme?.find?.(({ value }) => value === key))
          .map((font) => ({
            label: font.family,
            value: font.key,
          }));
      },

      updateSelected() {
        const { fontFamily = null } = getStyles();
        this.selected = fontFamily ?? this.fallback;
      },

      itemLabelFontFamily(value) {
        const themeFont = value?.match?.(/om-font-(\d)/)?.[1] ?? null;

        if (themeFont) {
          const themeFontIndex = Number(themeFont) - 1;
          const themeFontKey = this.getCustomThemeKitValues.fonts[themeFontIndex];
          return this.installedFonts.find(({ key }) => key === themeFontKey)?.family;
        }

        const { family } = this.installedFonts.find(({ key }) => key === value) ?? {};

        return family;
      },
      searchFont(family) {
        const font = Object.entries(this.fonts).find((entry) => entry[1].family === family);

        if (!font) return null;

        const [key = null] = font ?? [];

        return key;
      },
    },
  };
</script>

<style lang="sass">
  @import '@/sass/variables/_colors'
  .ds-dropdown-menu
    &.font-family
      width: 160px
  .font-family-toolbar-menu-label
    width: 160px
    text-overflow: ellipsis
    overflow: hidden
    white-space: nowrap
  .dropdown-footer
    position: sticky
    bottom: 0
    background: #fff
    font-size: .875rem
    padding: 8px 12px
    border-top: 1px solid #E3E5E8
</style>

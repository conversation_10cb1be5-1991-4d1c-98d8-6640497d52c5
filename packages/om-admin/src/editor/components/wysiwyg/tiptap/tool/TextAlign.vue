<template lang="pug">
DropdownMenu#text-align-toolbar-menu(
  ghost
  horizontal
  iconList
  highlightSelected
  :items="options"
  iconListSize="24px"
  :tooltip="$t('tiptap.alignAndIndent')"
  @toolbarToggled="handleToolbarOpen"
  v-model="value"
)
  template(slot="trigger")
    component(v-if="selected" :is="selectedIcon" size="24px")
    UilAlignCenter(v-else size="24px")
</template>

<script>
  import { mapState } from 'vuex';
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilAlignLeft, UilAlignCenter, UilAlignRight } from '@iconscout/vue-unicons';
  import itemMixin from '@/editor/mixins/item';
  import commonMixin from './mixins/common';

  const ucFirst = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  export default {
    name: 'TextAlign',
    components: { DropdownMenu, UilAlignLeft, UilAlignCenter, UilAlignRight },
    mixins: [commonMixin, itemMixin],
    data: () => ({ selected: null }),
    computed: {
      ...mapState(['mobilePreview']),
      options() {
        return [
          { label: this.$t('left'), icon: UilAlignLeft, value: 'left' },
          { label: this.$t('center'), icon: UilAlignCenter, value: 'center' },
          { label: this.$t('right'), icon: UilAlignRight, value: 'right' },
        ];
      },
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          if (this.mobilePreview) {
            this.setValueOf('mobile.textAlign', value);
          } else {
            this.$emit('editorCommand', { command: 'setTextAlign', value });
          }
          this.$nextTick(() => {
            this.extractTextAlign();
          });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'textAlignChange',
              settingName: 'textAlign',
              settingValue: value,
            },
          });
        },
      },
      selectedIcon() {
        return `UilAlign${ucFirst(this.selected)}`;
      },
    },
    mounted() {
      this.$bus.$on('wysiwygUpdate', () => {
        this.$nextTick(() => {
          this.extractTextAlign();
        });
      });
      this.init();
    },
    methods: {
      init() {
        this.extractTextAlign();
      },
      extractTextAlign() {
        if (this.mobilePreview) {
          this.selected = this.getValueOf('mobile.textAlign');
          return;
        }

        const { state = {} } = window.top?._editor ?? {};
        const { selection = {} } = state;
        const { $from = {}, $to = {} } = selection;

        const alignUnique = new Set();
        if ($from?.parent?.attrs?.textAlign) {
          alignUnique.add($from.parent.attrs.textAlign);
        }
        if ($to?.parent?.attrs?.textAlign) {
          alignUnique.add($to.parent.attrs.textAlign);
        }

        const aligns = Array.from(alignUnique.values());

        this.selected = aligns.length > 1 ? null : aligns[0] ?? 'center';
        this.$forceUpdate();
      },
    },
  };
</script>

import { mapState } from 'vuex';

export default {
  computed: {
    ...mapState(['selectedElement']),
    ...mapState('wysiwyg', ['visible']),
  },
  watch: {
    'selectedElement.uid': {
      handler() {
        this.closeDropdown();
      },
      immediate: true,
    },
    visible(visible) {
      if (!visible) {
        this.closeDropdown();
      }
    },
  },
  methods: {
    init() {},
    closeDropdown() {
      this.$children.forEach((child) => child?.hideMenu?.());
    },
    _report(id) {
      this.$bus.$emit('reportEditorEvent', {
        interactionType: 'buttonClicked',
        location: 'toolbar',
        extras: {
          elementType: this.selectedElement.type,
          buttonLabel: id.replace('-toolbar-menu', ''),
        },
      });
    },
    handleToolbarOpen({ open, id }) {
      if (!open) return;

      this._report(id);
    },
  },
};

<template lang="pug">
om-button#color-toolbar-menu.cursor-pointer.p-0(
  ghost
  small
  @click.stop="togglePicker"
  v-tooltip="{ content: $t('tiptap.fontColor'), trigger: 'hover', placement: 'top', popover: { arrow: false }, delay: { show: 350, hide: 0 }, classes: 'ds-dropdown-menu-tooltip' }"
)
  TextColorSVG.pointer-events-none(:style="{ color: code }")
</template>

<script>
  import tinycolor from 'tinycolor2';
  import { mapState } from 'vuex';
  import { QuillOmColor } from '@/utils/color-components/coloring';
  import itemMixin from '@/editor/mixins/item';
  import TextColorSVG from './icons/TextColor.svg.vue';
  import commonMixin from './mixins/common';
  import { getPaletteColorIndex } from '../helpers/paletteColors';
  import { getStyles } from '../helpers/getStyles';

  export default {
    name: 'Color',
    components: { TextColorSVG },
    mixins: [commonMixin, itemMixin],
    data: () => ({
      code: null,
      colorInstance: null,
    }),
    computed: {
      ...mapState(['selectedElement', 'colorPicker', 'mobilePreview', 'globalStyle']),
      device() {
        return this.mobilePreview ? 'mobile' : 'desktop';
      },
      color: {
        get() {
          let c = this.selectedElement[this.device].color;
          const { color = null } = window._editor?.getAttributes?.('textStyle') ?? {};

          if (!c || c !== color) {
            c = getPaletteColorIndex(color) ?? c;
          }

          return c;
        },
        set({ index, code }) {
          if (!index && !code) return;
          const value = this.getColorKey(index) ?? code;
          this.selectedElement[this.device].color = index ?? code;
          this.$emit('editorCommand', { command: 'setColor', value });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'colorChange',
              settingName: 'color',
              settingValue: value,
            },
          });
        },
      },
      templatePalette() {
        const hexColors = [];
        if (!this.globalStyle?.palette) {
          return hexColors;
        }

        hexColors.push(this.globalStyle.palette.mainColor);

        if (this.globalStyle.palette.secondaryColors) {
          hexColors.push(...this.globalStyle.palette.secondaryColors);
        }

        if (this.globalStyle.palette.themeColors) {
          this.globalStyle.palette.themeColors.forEach(({ themeColor }) => {
            if (themeColor.match(/^rgb\(.*\)$/)) {
              hexColors.push(tinycolor(themeColor).toHexString());
            } else {
              hexColors.push(themeColor);
            }
          });
        }

        return hexColors;
      },
    },
    watch: {
      globalStyle: {
        handler: 'updateSelected',
        deep: true,
      },
      'selectedElement.uid': {
        handler() {
          this.init();
        },
        deep: true,
      },
    },
    mounted() {
      this.init();
    },
    beforeDestroy() {
      this.$bus.$off('sketchChangeColor');
      this.$bus.$off('wysiwygUpdate', this.updateSelected);
    },
    methods: {
      init() {
        this.$bus.$off('sketchChangeColor');
        this.$bus.$off('wysiwygUpdate', this.updateSelected);
        this.colorInstance = new QuillOmColor(this.templatePalette);
        this.colorInstance.setColor(this.color);
        this.$bus.$on('sketchChangeColor', this.colorUpdate);
        this.$bus.$on('wysiwygUpdate', this.updateSelected);
        this.updateSelected();
      },
      updateSelected() {
        if (!window.top._editor) return;

        const { color } = getStyles();
        if (color) {
          const index = getPaletteColorIndex(color);
          this.code = index !== null ? this.templatePalette[index] : color;
        } else {
          this.code = null;
        }
      },
      togglePicker(event) {
        this.$emit('click');
        if (this.colorPicker.show) {
          this.$bus.$emit('hideColorPicker');
          return;
        }

        const innerHeight = window.innerHeight;
        const innerWidth = window.innerWidth;
        const colorPickerHeight = 520;
        const colorPickerWidth = 270;

        const left =
          innerWidth < event.pageX + colorPickerWidth ? innerWidth - colorPickerWidth : event.pageX;
        const top =
          innerHeight < event.pageY + colorPickerHeight
            ? innerHeight - colorPickerHeight
            : event.pageY;

        const params = {
          top,
          left,
          source: `editor-${this.selectedElement.uid}`,
          value: this.color,
          property: 'omcolor',
          alpha: false,
          colorInstance: this.colorInstance,
        };

        this.$bus.$emit('showColorPicker', params);
        this.handleToolbarOpen({ open: true, id: 'color-picker-toolbar-menu' });
      },
      getColorKey(index) {
        if (index === null) return null;
        return index === 0 ? 'main' : `c${index}`;
      },
      colorUpdate({ code, index }) {
        this.code = code;
        this.color = { index, code };
      },
    },
  };
</script>

<style lang="sass" scoped>
  #color-toolbar-menu
    min-width: auto
</style>

<template lang="pug">
DropdownMenu#dynamic-text.dynamic-text-dropdown(
  ghost
  showCheckMark
  contentWidth="260px"
  :groupLabels="groupLabels"
  :items="options"
  v-model="value"
)
  template(#trigger)
    OmButton(ghost icon="bolt-alt") Dynamic text
  template(#content-after)
    .dropdown-footer
      OmLink(primary withIconLeft)
        template(slot="left-icon")
          UilQuestionCircle.mr-2(size="24px")
        a(:href="howItWorksLink" target="_blank") {{ $t('howItWorks') }}
</template>

<script>
  import { mapState, mapGetters } from 'vuex';
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilQuestionCircle } from '@iconscout/vue-unicons';
  import { getDynamicTextGroupItems } from '@/editor/quill_options';
  import commonMixin from './mixins/common';

  export default {
    name: 'DynamicText',
    components: {
      DropdownMenu,
      UilQuestionCircle,
    },
    mixins: [commonMixin],
    data: () => ({
      selected: null,
    }),
    computed: {
      ...mapState(['ppoVariableNames', 'isShopifyActive', 'isShoprenterActive']),
      ...mapGetters(['isEmbedded']),
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          const selected = Object.values(this.options)
            .flatMap((option) => option)
            .find((option) => option.value === value);
          this.$bus.$emit('smart-tag-modal', {
            new: {
              currentSelection: null,
              optionValue: value,
              label: selected.label.replace('smartTags.tags.', ''),
              tiptap: true,
            },
          });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'dynamicTextInserted',
              settingName: 'dynamic-text',
              settingValue: value,
            },
          });
        },
      },
      howItWorksLink() {
        return this.$i18n.locale === 'en'
          ? 'https://support.optimonk.com/hc/en-us/articles/205906081-Smart-Tags'
          : 'https://support.optimonk.hu/hc/hu/articles/206662549-Dynamic-Text-Replacement-Dinamikus-sz%C3%B6vegcsere';
      },
      groupLabels() {
        return [
          this.$t('smartTags.tagGroups.personal_data'),
          this.$t('smartTags.tagGroups.location_and_time'),
          this.$t('smartTags.tagGroups.system'),
          this.$t('smartTags.tagGroups.history'),
          this.$t('smartTags.tagGroups.product_and_cart'),
        ];
      },
      options() {
        return getDynamicTextGroupItems(
          this.ppoVariableNames,
          this.isEmbedded,
          this.isShopifyActive || this.isShoprenterActive,
        );
      },
    },
  };
</script>

<style lang="sass">
  .dynamic-text-dropdown
    .btn-link
      padding-left: 0.25rem !important
      &:hover,
      &:active
        background-color: unset !important
        border-color: transparent
</style>

<template lang="pug">
DropdownMenu#font-weight-toolbar-menu(
  ghost
  :items="options"
  v-model="value"
  triggerIcon="angle-down"
  triggerIconClose="angle-up"
  highlightSelected
  placement="right"
  wrapperClass="font-weight"
  @toolbarToggled="handleToolbarOpen"
  :tooltip="$t('tiptap.fontWeight')"
)
  template(#trigger)
    .font-weight-toolbar-menu-label {{ selectedLabel }}
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import commonMixin from './mixins/common';
  import { getStyles } from '../helpers/getStyles';

  // TODO: make dynamic based on current font... pick most correct if font changes
  const DEFAULT_WEIGHT = '400';
  const WEIGHT_OPTIONS = [
    { value: '100', label: 'Thin' },
    { value: '200', label: 'Extra Light' },
    { value: '300', label: 'Light' },
    { value: '400', label: 'Normal' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semi Bold' },
    { value: '700', label: 'Bold' },
    { value: '800', label: 'Extra Bold' },
    { value: '900', label: 'Black' },
  ];

  export default {
    name: 'FontWeight',
    components: { DropdownMenu },
    mixins: [commonMixin],
    data: () => ({
      selected: null,
    }),
    computed: {
      options() {
        // TODO: make dynamic based on current font... pick most correct if font changes
        return WEIGHT_OPTIONS;
      },
      selectedLabel() {
        return this.options.find(({ value }) => value === this.selected)?.label ?? 'Normal';
      },
      selectedValue() {
        return this.options.find(({ value }) => value === this.selected)?.value ?? DEFAULT_WEIGHT;
      },
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          this.$emit('editorCommand', { command: 'setFontWeight', value });
          this.$nextTick(() => {
            this.updateSelected();
          });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'fontWeightChange',
              settingName: 'fontWeight',
              settingValue: value,
            },
          });
        },
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        this.updateSelected();
      },
      updateSelected() {
        const { fontWeight } = getStyles();
        this.selected = fontWeight ?? DEFAULT_WEIGHT;
      },
    },
  };
</script>

<style lang="sass">
  .font-weight-toolbar-menu-label
    width: 68px
    text-overflow: ellipsis
    overflow: hidden
    white-space: nowrap
</style>

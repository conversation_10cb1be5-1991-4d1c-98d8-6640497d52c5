<template lang="pug">
DropdownMenu#text-decoration-toolbar-menu(
  ghost
  :items="options"
  horizontal
  iconList
  iconListSize="24px"
  highlightSelected
  v-model="value"
  multiple
  @toolbarToggled="handleToolbarOpen"
  :tooltip="$t('tiptap.style')"
)
  template(slot="trigger")
    ItalicUnderlineSVG
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilUnderline, UilAlignLeft, UilItalic } from '@iconscout/vue-unicons';
  import commonMixin from './mixins/common';
  import ItalicUnderlineSVG from './icons/ItalicUnderline.svg.vue';
  import StrikeSVG from './icons/Strike.svg.vue';
  import UppercaseSVG from './icons/Uppercase.svg.vue';
  import { getStyles } from '../helpers/getStyles';

  function difference(array1, array2) {
    const set1 = new Set(array1);
    const set2 = new Set(array2);

    const difference1 = array1.filter((item) => !set2.has(item));
    const difference2 = array2.filter((item) => !set1.has(item));

    return [...difference1, ...difference2];
  }

  export default {
    name: 'TextStyles',
    components: {
      DropdownMenu,
      UilUnderline,
      StrikeSVG,
      ItalicUnderlineSVG,
      UilAlignLeft,
      UilItalic,
    },
    mixins: [commonMixin],
    data: () => ({
      selected: [],
    }),
    computed: {
      options() {
        return [
          { label: this.$t('italic'), icon: UilItalic, value: 'italic' },
          { label: this.$t('underline'), icon: UilUnderline, value: 'underline' },
          { label: this.$t('strike'), icon: StrikeSVG, value: 'line-through', command: 'strike' },
          { label: this.$t('uppercase'), icon: UppercaseSVG, value: 'uppercase' },
        ];
      },
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          const diff = difference(this.value, value);
          diff.forEach((v) => {
            const opt = this.options.find((o) => o.value === v);
            const rawCommand = opt?.command ?? v;
            const command = `toggle${rawCommand.charAt(0).toUpperCase() + rawCommand.slice(1)}`;
            this.$emit('editorCommand', {
              command,
            });
            this.$bus.$emit('reportEditorEvent', {
              interactionType: 'settingChanged',
              location: 'toolbar',
              extras: {
                elementType: this.selectedElement.type,
                eventType: 'textDecorationChange',
                settingValue: command,
              },
            });
          });
          this.$nextTick(() => {
            this.updateSelected();
          });
        },
      },
    },
    mounted() {
      this.updateSelected();
    },
    methods: {
      init() {
        this.updateSelected();
      },
      updateSelected() {
        const editor = window?._editor;
        if (!editor) return;

        const selected = this.options
          ?.map?.((opt) => {
            const { value } = opt;
            const { textTransform, textDecoration = '', textItalic } = getStyles() ?? {};

            return [textTransform, ...(textDecoration?.split?.(' ') ?? []), textItalic]
              .filter(Boolean)
              .includes(value)
              ? value
              : null;
          })
          ?.filter?.(Boolean);

        this.selected = selected;
      },
    },
  };
</script>

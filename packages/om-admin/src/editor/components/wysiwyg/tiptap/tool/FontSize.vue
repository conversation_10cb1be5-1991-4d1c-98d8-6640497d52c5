<template lang="pug">
DropdownMenu#font-size-toolbar-menu(
  ghost
  :items="options"
  v-model="value"
  highlightSelected
  placement="right"
  wrapperClass="font-size"
  @toolbarToggled="handleToolbarOpen"
  :tooltip="$t('tiptap.fontSize')"
)
  template(#trigger)
    .d-flex.font-size-toolbar-menu-label {{ selected }}
      .d-flex.align-items-center.justify-content-center.flex-column.ml-1
        .font-size-toolbar-menu-label-icon-wrapper.up
          UilAngleUp.font-size-toolbar-menu-label-icon.up(
            size="24px"
            @click.native.stop="increase"
          )
        .font-size-toolbar-menu-label-icon-wrapper.down
          UilAngleDown.font-size-toolbar-menu-label-icon.down(
            size="24px"
            @click.native.stop="decrease"
          )
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilAngleDown, UilAngleUp } from '@iconscout/vue-unicons';
  import commonMixin from './mixins/common';
  import { getStyles } from '../helpers/getStyles';

  export default {
    name: 'FontSize',
    components: { DropdownMenu, UilAngleDown, UilAngleUp },
    mixins: [commonMixin],
    data: () => ({
      selected: null,
    }),
    computed: {
      options() {
        return [8, 9, 10, 11, 12, 14, 16, 18, 24, 30, 36, 48, 60, 75, 100].map((size) => ({
          label: size,
          value: size,
        }));
      },
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          this.$emit('editorCommand', { command: 'setFontSize', value });
          this.$nextTick(() => {
            this.updateSelected();
          });
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'fontSizeChange',
              settingName: 'fontSize',
              settingValue: value,
            },
          });
        },
      },
      upStep() {
        return this.value < 70 ? 1 : 5;
      },
      downStep() {
        return this.value <= 70 ? 1 : 5;
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        this.updateSelected();
      },
      updateSelected() {
        const { fontSize } = getStyles();
        this.selected = Number(fontSize ?? 16);
      },
      increase() {
        this.value = this.selected + this.upStep;
        this.$nextTick(() => {
          this.updateSelected();
        });
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'settingChanged',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            eventType: 'fontSizeIncrease',
            settingName: 'fontSize',
            settingValue: this.value,
          },
        });
      },
      decrease() {
        this.value = this.selected - this.downStep;
        this.$nextTick(() => {
          this.updateSelected();
        });
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'settingChanged',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            eventType: 'fontSizeDecrease',
            settingName: 'fontSize',
            settingValue: this.value,
          },
        });
      },
    },
  };
</script>

<style lang="sass">
  .ds-dropdown-menu
    &.font-size
      width: 74px
  .font-size-toolbar-menu-label
    width: 74px
    text-overflow: ellipsis
    overflow: hidden
    white-space: nowrap
    position: relative
    &-icon-wrapper
      position: absolute
      right: -4px
      z-index: 9999
      height: 18px
      overflow: hidden
      display: flex
      align-items: center
      &.up
        top: -6px
        padding-top: 6px
      &.down
        bottom: -6px
        padding-bottom: 6px
      &:hover
        z-index: 999999
</style>

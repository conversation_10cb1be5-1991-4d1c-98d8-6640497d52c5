<template lang="pug">
DropdownMenu#emoji-toolbar-menu(ghost contentWidth="300px" ref="popper" @toolbarToggled="handleToolbarOpen" :tooltip="$t('tiptap.emoji')")
  template(#trigger)
    GroupIconSmile(size="24px")
  #emoji-toolbar-menu-search-wrapper.d-flex.align-items-center.justify-content-center
    //- UilSearch.ds-dropdown-menu-search-icon(size="1.5em")
    OmInput#emoji-toolbar-menu-input.w-100(
      v-model="searchValue"
      type="text"
      :placeholder="$t('tiptap.searchEmoji')"
      small
    )
  om-pills.justify-content-between.mt-2(v-if="!searchValue" :pills="groups" :selected="group" @select="group = $event" key :needMargin="false" :needPadding="false")
    template(#default="{ key, text }")
      component(:is="`group-icon-${key}`" :title="text" size="24px")
  .emoji-list.mt-2(:class="{ 'one-frame': lowSearchCharCount || !list.length  }")
    span(v-if="lowSearchCharCount") {{ $t('tiptap.emojiModal.typeMore') }}
    span(v-else-if="!list.length") {{ $t('tiptap.emojiModal.empty') }}
    span.emoji-list-item.cursor-pointer(v-else v-for="emoji in list" :title="emoji.name" @click="insert(emoji)") {{ emoji.char }}
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import {
    UilSmile as GroupIconSmile,
    UilSearch,
    UilPlaneDeparture as GroupIconPlaneDeparture,
    UilFootballAmerican as GroupIconFootballAmerican,
    UilLightbulbAlt as GroupIconLightbulbAlt,
  } from '@iconscout/vue-unicons';
  import Input from '@/components/Elements/Input/Input.vue';
  import groupedEmojis from '@/editor/components/wysiwyg/tiptap/extensions/emoji/list';
  import GroupIconBurger from '@/editor/components/wysiwyg/tiptap/tool/icons/Burger.svg.vue';
  import GroupIconLeaf from '@/editor/components/wysiwyg/tiptap/tool/icons/Leaf.svg.vue';
  import GroupIconPeace from '@/editor/components/wysiwyg/tiptap/tool/icons/Peace.svg.vue';
  import GroupIconFlag from '@/editor/components/wysiwyg/tiptap/tool/icons/Flag.svg.vue';
  import commonMixin from './mixins/common';

  const REGROUPING_KEYS = {
    'Smileys & Emotion': 'smile',
    'People & Body': 'smile',
    'Animals & Nature': 'leaf',
    'Food & Drink': 'burger',
    'Travel & Places': 'plane-departure',
    Activities: 'football-american',
    Objects: 'lightbulb-alt',
    Component: 'lightbulb-alt',
    Symbols: 'peace',
    Flags: 'flag',
  };

  const GROUP_ORDER = [
    'smile',
    'leaf',
    'burger',
    'plane-departure',
    'football-american',
    'lightbulb-alt',
    'peace',
    'flag',
  ];

  export default {
    name: 'LineHeight',
    components: {
      DropdownMenu,
      Input,
      UilSearch,
      GroupIconSmile,
      GroupIconPlaneDeparture,
      GroupIconFootballAmerican,
      GroupIconLightbulbAlt,
      GroupIconBurger,
      GroupIconLeaf,
      GroupIconPeace,
      GroupIconFlag,
    },
    mixins: [commonMixin],
    data: () => ({
      groupedEmojis,
      group: 'smile',
      searchValue: null,
      selected: null,
    }),
    computed: {
      groups() {
        return GROUP_ORDER.map((key) => ({ key, text: key }));
      },
      regrouped() {
        const groups = {};
        Object.entries(this.groupedEmojis).forEach(([group, emojis]) => {
          const newGroup = REGROUPING_KEYS[group];
          if (!groups[newGroup]) {
            groups[newGroup] = [];
          }
          groups[newGroup].push(...emojis);
        });
        return groups;
      },
      list() {
        if (this.searchValue) return this.filtered;
        return this.regrouped[this.group];
      },
      trimmedSearchValue() {
        return this.searchValue?.trim?.() ?? '';
      },
      lowSearchCharCount() {
        return this.searchValue && this.trimmedSearchValue?.length < 3;
      },
      filtered() {
        if (this.lowSearchCharCount) return [];

        return Object.values(this.groupedEmojis)
          .flat()
          .filter((emoji) =>
            [emoji.name, emoji.subgroup].some((name) =>
              new RegExp(this.trimmedSearchValue, 'gm').test(name),
            ),
          );
      },
    },
    methods: {
      insert(emoji) {
        this.$emit('editorCommand', { command: 'insertContent', value: emoji.char });
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'settingChanged',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            eventType: 'emojiInserted',
            settingName: 'emoji',
            settingValue: emoji.char,
          },
        });
        this.$refs.popper.hideMenu();
      },
    },
  };
</script>

<style lang="sass">
  .emoji-list
    width: 100%
    display: grid
    grid-template-columns: repeat(8, 1fr)
    grid-template-rows: repeat(8, 1fr)
    gap: .25rem
    height: 12.5rem
    font-size: 2rem
    &.one-frame
      grid-template-columns: 1fr
      grid-template-rows: 1fr
      font-size: 1rem
    &-item
      min-height: 30px
      width: 30px
      text-align: center
      font-size: 24px
</style>

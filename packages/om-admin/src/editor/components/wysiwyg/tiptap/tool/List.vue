<template lang="pug">
DropdownMenu#list-toolbar-menu(
  ghost
  horizontal
  iconList
  highlightSelected
  :items="options"
  iconListSize="24px"
  @toolbarToggled="handleToolbarOpen"
  v-model="value"
  :tooltip="$t('tiptap.list')"
)
  template(slot="trigger")
    component(v-if="selected" :is="selectedIcon" size="24px")
    UilListUl(v-else size="24px")
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilListUl, UilListOl } from '@iconscout/vue-unicons';
  import commonMixin from './mixins/common';

  export default {
    name: 'List',
    components: { DropdownMenu, UilListUl, UilListOl },
    mixins: [commonMixin],
    data: () => ({ selected: null }),
    computed: {
      options() {
        return [
          { label: 'Bullet', icon: UilListUl, value: 'bullet' },
          { label: 'Ordered', icon: UilListOl, value: 'ordered' },
        ];
      },
      value: {
        get() {
          return this.selected;
        },
        set(value) {
          this.selected = this.options.find((option) => option.value === value)?.value;
          if (value === 'bullet') {
            window.top._editor.chain().focus().toggleBulletList().run();
          } else {
            window.top._editor.chain().focus().toggleOrderedList().run();
          }

          this.setSelected();
        },
      },
      selectedIcon() {
        const isOrdered = this.selected === 'ordered';
        return `UilList${isOrdered ? 'Ol' : 'Ul'}`;
      },
    },
    mounted() {
      this.setSelected();
    },
    methods: {
      setSelected() {
        const isOrderedList = window.top?._editor?.isActive('orderedList');
        const isBulletList = window.top?._editor?.isActive('bulletList');

        if (!isOrderedList && !isBulletList) {
          this.selected = null;
        }

        if (isOrderedList) {
          this.selected = this.options.find((option) => option.value === 'ordered')?.value;
        }

        if (isBulletList) {
          this.selected = this.options.find((option) => option.value === 'bullet')?.value;
        }
      },
    },
  };
</script>

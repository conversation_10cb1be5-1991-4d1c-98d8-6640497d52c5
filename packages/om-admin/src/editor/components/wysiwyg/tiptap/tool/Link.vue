<template lang="pug">
DropdownMenu#link-toolbar-menu(
  ghost
  ref="popper"
  @toolbarToggled="handleToolbarOpen"
  :tooltip="$t('tiptap.link')"
)
  template(slot="trigger")
    component(
      :is="href ? 'UilLinkBroken' : 'UilLinkAlt'"
      size="24px"
      :style="{ color: href ? 'var(--brand-primary-color)' : null }"
    )
      span "{{ href }}"
  .d-flex.p-1.link-toolbar-menu-content
    OmInput#link-toolbar-menu-input(
      small
      :value="href"
      @input="input = $event"
      placeholder="https://example.org"
    )
    OmButton.px-3(v-if="value" small secondary @click="apply(null)") {{ $t('deleteLink') }}
    OmLink.px-3(primary @click="value = input") {{ value ? $t('modify') : $t('apply') }}
</template>

<script>
  import DropdownMenu from '@/components/Elements/DropdownMenu/DropdownMenu.vue';
  import { UilLinkAlt, UilLinkBroken } from '@iconscout/vue-unicons';
  import commonMixin from './mixins/common';
  import { getStyles } from '../helpers/getStyles';

  export default {
    name: 'Link',
    components: { DropdownMenu, UilLinkAlt, UilLinkBroken },
    mixins: [commonMixin],
    data: () => ({ href: null, input: null }),
    computed: {
      value: {
        get() {
          return this.href;
        },
        set(value) {
          this.apply(value);
          this.$refs.popper.hideMenu();
        },
      },
    },
    watch: {
      'selectedElement.uid': {
        handler() {
          this.$nextTick(() => {
            this.updateSelected();
          });
        },
        immediate: false,
      },
    },
    mounted() {
      this.init();
      this.$bus.$on('selectionUpdate', this.updateSelected);
    },
    beforeDestroy() {
      this.$bus.$off('selectionUpdate', this.updateSelected);
    },
    methods: {
      init() {
        this.updateSelected();
      },
      updateSelected() {
        // Get href from textStyle mark's linkAttrs instead of link mark
        const textStyleAttrs = window.top._editor?.getAttributes?.('textStyle') ?? {};
        const href = textStyleAttrs.linkAttrs?.href || null;
        this.href = href;
        this.input = href;
      },
      apply(url) {
        const editor = window.top._editor;
        if (!url) {
          const styles = getStyles();
          editor
            .chain()
            .focus()
            .setMark('textStyle', { ...styles, linkAttrs: null })
            .run();

          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'removeLink',
              settingName: 'link',
              settingValue: url,
            },
          });
        } else {
          const styles = getStyles();

          // Only include link-specific attributes in linkAttrs
          const linkAttrs = { href: url };

          editor
            .chain()
            .focus()
            .setMark('textStyle', { ...styles, linkAttrs })
            .run();

          const { from, to } = editor.state.selection;
          const selectedText = editor.state.doc.textBetween(from, to, ' ');
          this.$bus.$emit('reportEditorEvent', {
            interactionType: 'settingChanged',
            location: 'toolbar',
            extras: {
              elementType: this.selectedElement.type,
              eventType: 'markToLink',
              settingName: 'link',
              settingValue: url,
              selectedText,
            },
          });
        }

        this.$nextTick(() => {
          this.updateSelected();
        });
      },
    },
  };
</script>

<style lang="sass">
  .link-toolbar-menu-content
    gap: .5rem
</style>

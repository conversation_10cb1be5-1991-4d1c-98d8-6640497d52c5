export function resetInnerMark(
  editor,
  markType,
  resetMarkType,
  givenChain = null,
  keepChaining = false,
  command = 'unsetMark',
) {
  const { state } = editor;
  const { selection } = state;
  const { from, to } = selection;

  console.log(
    'resetInnermark',
    markType?.name ?? markType,
    resetMarkType?.name ?? resetMarkType,
    keepChaining,
    command,
  );
  let needRun = false;
  const chain = givenChain ?? editor.chain();

  state.doc.nodesBetween(from, to, (node, pos) => {
    if (!node.isText) return;
    node.marks.forEach((mark) => {
      if (mark.type === markType || mark.type.name === markType) {
        needRun = true;
        chain
          .setTextSelection({ from: pos, to: pos + node.nodeSize })
          [command](resetMarkType, command !== 'unsetMark' ? {} : undefined)
          .setTextSelection({ from, to });
      }
    });
  });

  const shouldRun = needRun || givenChain;
  if (shouldRun && !keepChaining) {
    chain.run();
  }
}

export const RANKED_TYPES = ['link', 'textStyle'];

export function updateStyles({ chain, editor, state }, styles, updated = null) {
  const pipeline = chain();
  const allStyles = { ...styles };
  const { href: hasLink } = editor.getAttributes('link') ?? {};

  // Check if this is a "select all" scenario
  const { from, to } = state.selection;
  const isSelectAll = from === 0 && to === state.doc.content.size;

  // If select all, preserve existing styles from the current position
  if (isSelectAll) {
    const currentStyles = editor.getAttributes('textStyle') ?? {};
    // Merge current styles with new styles, giving priority to new styles
    Object.assign(allStyles, { ...currentStyles, ...styles });
  }

  // Always apply textStyle mark for direct text formatting
  pipeline.setMark('textStyle', allStyles);

  RANKED_TYPES.forEach((type, index) => {
    if (type === updated) return;

    const applyStyles = { ...allStyles };
    if (hasLink && index > 0) {
      applyStyles.fontSize = null;
    }

    pipeline.updateAttributes(type, applyStyles);
  });

  pipeline.focus();
  pipeline.run();
}

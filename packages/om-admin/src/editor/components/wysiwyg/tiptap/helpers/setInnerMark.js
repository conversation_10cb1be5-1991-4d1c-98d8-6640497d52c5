export function setInnerMark(editor, markType, setMarkType, attributes, given<PERSON>hain) {
  const { state } = editor;
  const { selection } = state;
  const { from, to } = selection;

  const chain = givenChain ?? editor.chain();

  state.doc.nodesBetween(from, to, (node, pos) => {
    if (!node.isText) return;
    node.marks.forEach((mark) => {
      if (mark.type === markType) {
        chain
          .setTextSelection({ from: pos, to: pos + node.nodeSize })
          .setMark(setMarkType, attributes)
          .setTextSelection({ from, to });
      }
    });
  });
}

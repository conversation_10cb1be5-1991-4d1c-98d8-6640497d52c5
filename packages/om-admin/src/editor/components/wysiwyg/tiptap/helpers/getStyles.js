const MARKS = ['textStyle', 'paragraph'];

export function getStyles(marks = <PERSON><PERSON><PERSON>, editor) {
  let styles = {};

  editor = editor || window.parent.top._editor;

  const savedFamily = editor.options.element.style.fontFamily;

  marks?.forEach?.((mark) => {
    const markStyles = editor?.getAttributes?.(mark) ?? {};

    styles = {
      ...styles,
      ...markStyles,
    };
  });

  return styles;
}

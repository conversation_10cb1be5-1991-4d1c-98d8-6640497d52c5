import { getStyles } from './getStyles';
import { RANKED_TYPES } from './updateStyles';

export function toggleCommand(ctx, type) {
  const { editor, chain, state } = ctx;
  const { from, to } = state.selection;
  const { fontSize, ...otherStyles } = getStyles();

  const pipeline = chain();

  pipeline.toggleMark(type, otherStyles);

  state.doc.nodesBetween(from, to, (node, pos) => {
    const rootMark = node?.marks?.[0];

    if (!rootMark) {
      pipeline
        .setTextSelection({ from: pos, to: pos + node.nodeSize })
        .setMark('textStyle', otherStyles)
        .setTextSelection({ from, to });
    }

    node?.marks?.forEach?.((mark) => {
      pipeline.updateAttributes(mark?.type, otherStyles);
    });
  });

  pipeline.run();

  return true;
}

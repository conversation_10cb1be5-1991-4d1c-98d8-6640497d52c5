export function updateInnerMarks(editor, pipeline, styles, command) {
  const { state } = editor;
  const { selection } = state;
  const { from, to } = selection;

  state.doc.nodesBetween(from, to, (node) => {
    if (!node.isText) return;

    const hasLink = node.marks?.filter?.((mark) => mark?.type?.name === 'link');
    console.log('styles', node, hasLink, command);

    node.marks.forEach((mark, index) => {
      const attrs = !index ? styles : {};
      pipeline.updateAttributes(mark.type, attrs);
    });

    if (hasLink) {
      // pipeline.unsetMark('textStyle');
    }
  });
}

import { Extension } from '@tiptap/core';

export const OmTextAlign = Extension.create({
  name: 'textAlign',

  addGlobalAttributes() {
    return [
      {
        types: ['paragraph', 'heading'],
        attributes: {
          textAlign: {
            default: null,
            parseHTML: (element) => {
              if (element.classList.contains('om-dtr-content')) {
                const match = element.getAttribute('class')?.match(/ql-align-(\w+)/);
                return match ? match[1] : null;
              }
              return null;
            },
            renderHTML: (attributes) => {
              return {
                class: `ql-align-${attributes.textAlign ?? 'center'}`,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setTextAlign:
        (alignment) =>
        ({ commands }) => {
          const result = ['paragraph', 'heading']
            .map((type) => commands.updateAttributes(type, { textAlign: alignment }))
            .every(Boolean);

          commands.focus();
          return result;
        },

      unsetTextAlign:
        () =>
        ({ commands }) => {
          const result = ['paragraph', 'heading']
            .map((type) => commands.updateAttributes(type, { textAlign: null }))
            .every(Boolean);

          commands.focus();
          return result;
        },
    };
  },
});

import { Extension } from '@tiptap/core';
import { Plugin } from '@tiptap/pm/state';
import { Node as ProseMirrorNode, Slice } from 'prosemirror-model';

export const ApplyStyleToEmptyLines = Extension.create({
  name: 'applyStyleToEmptyLines',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          transformPasted: (content) => {
            // Convert empty lines to <p><br></p> to ensure they retain styles
            const parser = new DOMParser();
            const doc = parser.parseFromString(content.html, 'text/html');
            const paragraphs = doc.querySelectorAll('p');

            paragraphs.forEach((p) => {
              if (p.textContent.trim() === '') {
                p.innerHTML = '<br>';
              }
            });

            return new Slice(
              ProseMirrorNode.fromJSON(this.editor.schema, doc.body.firstChild),
              0,
              0,
            );
          },
        },
      }),
    ];
  },

  addCommands() {
    return {
      applyStyleToEmptyLines:
        (savedStyles) =>
        ({ tr }) => {
          if (!savedStyles) return false;

          const { doc, selection } = tr;
          const { from, to } = selection;

          doc.nodesBetween(from, to, (node, pos) => {
            if (node.type.name === 'paragraph' && node.content.size === 0) {
              tr.addMark(pos, 'textStyle', savedStyles);
            }
          });

          return true;
        },
    };
  },
});

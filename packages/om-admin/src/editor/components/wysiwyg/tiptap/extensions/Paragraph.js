import { mergeAttributes } from '@tiptap/core';
import Paragraph from '@tiptap/extension-paragraph';

export const OmParagraph = Paragraph.extend({
  addOptions() {
    return {
      HTMLAttributes: { class: 'om-dtr-content' },
    };
  },

  addAttributes() {
    return {
      lineHeight: {
        default: null,
      },
    };
  },

  addCommands() {
    return {
      setLineHeight:
        (lineHeight, isInput) =>
        ({ commands }) => {
          return commands.setLineHeight(lineHeight, isInput);
        },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div.om-dtr-content',
        getAttrs: (node) => {
          const align = node.getAttribute('class')?.match(/ql-align-(\w+)/)?.[1];
          const rawLineHeight = `${node.style.lineHeight}`;

          let lineHeight = rawLineHeight;
          if (rawLineHeight) {
            // Parse string line-height values (same logic as TextStyle)
            const trimmed = rawLineHeight.trim().toLowerCase();

            // Convert "normal" to 1.2 (standard browser default)
            if (trimmed === 'normal') {
              lineHeight = '1.2';
            }
          }

          return {
            textAlign: align || null,
            lineHeight,
          };
        },
      },
    ];
  },

  renderHTML(data) {
    const { HTMLAttributes } = data;

    if (HTMLAttributes.lineHeight) {
      HTMLAttributes.style = `line-height: ${HTMLAttributes.lineHeight}`;
      delete HTMLAttributes.lineHeight;
    }

    return ['div', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
  },
});

import SmartTagParser from '@om/smart-tags/src/Parser';
import { generateRandomStr } from '@/util';
import { Node } from '@tiptap/core';
import { getStyles } from '../helpers/getStyles';

export const DynamicText = Node.create({
  name: 'dynamicText',
  group: 'inline',
  inline: true,
  atom: true,
  selectable: true,

  addAttributes() {
    return {
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-smart-tag-id'),
        renderHTML: (attributes) => {
          if (!attributes.id) return {};
          return { 'data-smart-tag-id': attributes.id };
        },
      },
      attribute: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-smart-tag'),
        renderHTML: (attributes) => {
          if (!attributes.attribute) return {};
          return { 'data-smart-tag': attributes.attribute };
        },
      },
      label: {
        default: null,
        parseHTML: (element) => {
          const content = element.querySelector('span, .smart-tag-content');
          return content ? content.textContent : null;
        },
        renderHTML: () => {}, // Label is rendered in the content, not as attribute
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span.smart-tag',
        priority: 100,
        getAttrs: (dom) => {
          const smartTag = SmartTagParser.parse(dom, this.$t);
          const label = dom.querySelector('span, .smart-tag-content')?.textContent;

          if (!smartTag) {
            console.warn('Unable to parse dynamic text: ', dom);
            return false;
          }

          return {
            attribute: smartTag.attribute,
            label,
            id: smartTag.uid,
          };
        },
      },
    ];
  },

  renderHTML({ node, HTMLAttributes }) {
    return [
      'span',
      {
        ...HTMLAttributes,
        class: 'smart-tag',
        'data-smart-tag': node.attrs.attribute,
        'data-smart-tag-id': node.attrs.id || generateRandomStr(7),
        contenteditable: 'false',
      },
      ['span', { class: 'smart-tag-content', contenteditable: 'false' }, node.attrs.label],
    ];
  },

  addNodeView() {
    return ({ node, view, getPos }) => {
      const wrapper = document.createElement('span');
      wrapper.classList.add('smart-tag');
      wrapper.setAttribute('contenteditable', 'false');
      wrapper.setAttribute('data-smart-tag', node.attrs.attribute);
      wrapper.setAttribute('data-smart-tag-id', node.attrs.id || generateRandomStr(7));

      const inner = document.createElement('span');
      inner.classList.add('smart-tag-content');
      inner.setAttribute('contenteditable', 'false');
      inner.textContent = node.attrs.label;

      wrapper.appendChild(inner);

      // Simple click handler for editing
      wrapper.addEventListener('click', (event) => {
        event.preventDefault();
        window.parent.om.bus.$emit('smart-tag-modal', {
          edit: {
            smartTagElement: wrapper,
            tiptap: true,
          },
        });
      });

      // Double-click to select the node
      wrapper.addEventListener('dblclick', (event) => {
        event.preventDefault();
        event.stopPropagation();

        if (typeof getPos === 'function') {
          const pos = getPos();
          const { state, dispatch } = view;
          dispatch(
            state.tr.setSelection(
              state.selection.constructor.create(state.doc, pos, pos + node.nodeSize),
            ),
          );
        }
      });

      return {
        dom: wrapper,
        contentDOM: null,
      };
    };
  },

  addCommands() {
    return {
      insertDynamicText:
        (attributes) =>
        ({ chain, state, editor }) => {
          const { selection } = state;
          const { from, to } = selection;

          // Capture current styles from the editor
          const currentStyles = getStyles(['textStyle', 'link'], editor);

          // Create the dynamic text node with current marks
          const dynamicTextContent = {
            type: 'dynamicText',
            attrs: {
              id: attributes.id || generateRandomStr(7),
              attribute: attributes.attribute,
              label: attributes.label,
            },
            marks:
              Object.keys(currentStyles).length > 0
                ? [
                    {
                      type: 'textStyle',
                      attrs: currentStyles,
                    },
                  ]
                : [],
          };

          // Handle text selection deletion and insert the dynamic text node
          let pipeline = chain().focus();

          // If there's a text selection, delete it first
          if (from !== to) {
            pipeline = pipeline.deleteRange({ from, to });
          }

          // Insert the dynamic text node with styles
          return pipeline.insertContent(dynamicTextContent).run();
        },

      updateDynamicText:
        ({ id, attribute, label }) =>
        ({ chain, state }) => {
          const { doc } = state;
          let targetPos = null;

          // Find the node with the matching ID
          doc.descendants((node, pos) => {
            if (node.type.name === 'dynamicText' && node.attrs.id === id) {
              targetPos = pos;
              return false; // Stop searching
            }
            return true;
          });

          if (targetPos === null) {
            console.warn('Could not find dynamicText node with ID:', id);
            return false;
          }

          chain()
            .setNodeSelection(targetPos)
            .updateAttributes('dynamicText', { attribute, label })
            .run();

          return true;
        },
    };
  },
});

import OrderedList from '@tiptap/extension-ordered-list';
import { getNodeType } from '@tiptap/core';
import { updateStyles } from '../helpers/updateStyles';
import { getStyles } from '../helpers/getStyles';

export const OmOrderedList = OrderedList.extend({
  addCommands() {
    return {
      ...this.parent?.(),
      toggleOrderedList:
        () =>
        ({ commands, editor, state, dispatch, chain }) => {
          // Check if we're already in an ordered list
          if (editor.isActive('orderedList')) {
            // If we are, use the parent's toggle command to properly remove the list
            const result = this.parent().toggleOrderedList()({
              commands,
              editor,
              state,
              dispatch,
              chain,
            });
            updateStyles({ chain, editor, state }, getStyles());
            return result;
          }

          // If we're not in an ordered list, use our custom wrapInList command
          // that preserves formatting
          const orderedListType = getNodeType('orderedList', editor.schema);
          return commands.wrapInList(orderedListType);
        },
    };
  },
  addInputRules() {
    return [];
  },
});

import { wrapInList } from '@tiptap/pm/schema-list';
import ListItem from '@tiptap/extension-list-item';
import { getNodeType } from '@tiptap/core';
import { getPaletteColorIndex } from '../helpers/paletteColors';
import { getStyles } from '../helpers/getStyles';

function upsertListStyles() {
  const root = document.querySelector('#om-campaign-0');
  const styles = getStyles();

  if (styles.fontFamily) {
    const storedFamily = window.parent.top?.om?.store?.state?.fonts?.[styles.fontFamily]?.family
      ? `"${window.parent.top?.om?.store?.state?.fonts?.[styles.fontFamily]?.family}"`
      : null;
    let fontFamily =
      storedFamily ??
      `"${styles.fontFamily
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')}"`;

    const themeKitFontIndex = styles.fontFamily.match(/om-font-(\d)/)?.[1];

    if (themeKitFontIndex) {
      const themeKitFontIndexNumber = Number(themeKitFontIndex) - 1;
      fontFamily = `var(--om-font-${isNaN(themeKitFontIndexNumber) ? 0 : themeKitFontIndexNumber})`;
    }

    root.style.setProperty('--dyn-font-family', fontFamily);
  }

  if (styles.fontSize) {
    root.style.setProperty('--dyn-font-size', `${Number(styles.fontSize) / 16}em`);
  }
  if (styles.fontWeight) {
    root.style.setProperty('--dyn-font-weight', styles.fontWeight);
  }

  if (styles.color) {
    const colorIndex = getPaletteColorIndex(styles.color);
    if (colorIndex !== null) {
      root.style.setProperty('--dyn-color', `var(--om-color-${colorIndex})`);
      return;
    }
    root.style.setProperty('--dyn-color', styles.color);
  }
}

export const OmListItem = ListItem.extend({
  onCreate() {
    // Ez csak az Extension/Node/Mark onCreate-je nem egy új listaelem elkészülte
    // vagy több lista esetén különböző formázásokkal nem fog jól működni
    // illetve már most sem működik jól
    upsertListStyles();
  },
  addKeyboardShortcuts() {
    return {
      Enter: () => this.editor.commands.splitListItemWithStyles('listItem'),
      Backspace: ({ editor }) => {
        const { state } = editor;
        const { selection } = state;
        const { $anchor } = selection;
        const grandParent = $anchor.node($anchor.depth - 1);

        if ($anchor.parent.type.name === 'paragraph') {
          if (
            grandParent?.type?.name === 'listItem' &&
            ($anchor.parent.textContent === '' || $anchor.parent.textContent.trim() === '') &&
            $anchor.parentOffset === 0
          ) {
            const liftResult = editor.commands.liftListItem('listItem');
            if (liftResult) {
              // Also delete the empty paragraph node
              setTimeout(() => {
                if (editor.state.selection.$anchor.parent.textContent === '') {
                  editor.commands.deleteNode('paragraph');
                }
              }, 0);
            }
            return liftResult;
          }
        }

        return false;
      },
    };
  },
  addCommands() {
    return {
      ...this.parent?.(),
      splitListItemWithStyles:
        (typeOrName) =>
        ({ commands, editor }) => {
          upsertListStyles();
          const styles = getStyles();

          // Use the default splitListItem command
          const splitResult = commands.splitListItem(typeOrName);

          // If splitting succeeded, apply formatting to the new list item
          if (splitResult) {
            // Use requestAnimationFrame to ensure the DOM is updated
            requestAnimationFrame(() => {
              try {
                // Apply styles to the current selection (new list item)
                editor.chain().focus().setMark('textStyle', styles).run();

                // Update link attributes if needed
                if (editor.isActive('link')) {
                  editor.commands.updateAttributes('link', styles);
                }
              } catch (error) {
                console.warn('Failed to apply formatting to new list item:', error);
              }
            });
          }

          return splitResult;
        },
      wrapInList:
        (typeOrName, attributes = {}) =>
        ({ state, dispatch, editor }) => {
          upsertListStyles();
          // Get current styles before wrapping
          const styles = getStyles();

          // Get the node type for wrapping
          const type = getNodeType(typeOrName, editor.schema);

          // First perform the wrapping operation
          const wrapResult = wrapInList(type, attributes)(state, dispatch);

          // If wrapping succeeded, apply formatting using the chain API
          if (wrapResult) {
            // Use requestAnimationFrame to ensure the DOM is updated
            requestAnimationFrame(() => {
              try {
                // Apply styles to the current selection
                editor.chain().focus().setMark('textStyle', styles).run();

                // Update link attributes if needed
                if (editor.isActive('link')) {
                  editor.commands.updateAttributes('link', styles);
                }
              } catch (error) {
                console.warn('Failed to apply formatting to list:', error);
              }
            });
          }

          return wrapResult;
        },
      liftEmptyBlock:
        () =>
        ({ state }) => {
          const { selection } = state;
          const { $from, empty } = selection;

          if (!empty || $from.parent.content.size) return false;

          const textStyle = getStyles();
          const liftResult = this.editor.commands.liftListItem('listItem');

          if (liftResult) {
            this.editor.commands.setMark('textStyle', textStyle);
          }

          return liftResult;
        },
    };
  },
});

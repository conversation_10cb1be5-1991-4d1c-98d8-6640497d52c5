import BulletList from '@tiptap/extension-bullet-list';
import { getNodeType } from '@tiptap/core';
import { updateStyles } from '../helpers/updateStyles';
import { getStyles } from '../helpers/getStyles';

export const OmBulletList = BulletList.extend({
  addCommands() {
    return {
      ...this.parent?.(),
      toggleBulletList:
        () =>
        ({ commands, editor, state, dispatch, chain }) => {
          // Check if we're already in a bullet list
          if (editor.isActive('bulletList')) {
            // If we are, use the parent's toggle command to properly remove the list
            const result = this.parent().toggleBulletList()({
              commands,
              editor,
              state,
              dispatch,
              chain,
            });
            updateStyles({ chain, editor, state }, getStyles());
            return result;
          }

          // If we're not in a bullet list, use our custom wrapInList command
          // that preserves formatting
          const bulletListType = getNodeType('bulletList', editor.schema);
          return commands.wrapInList(bulletListType);
        },
    };
  },
  addInputRules() {
    return [];
  },
});

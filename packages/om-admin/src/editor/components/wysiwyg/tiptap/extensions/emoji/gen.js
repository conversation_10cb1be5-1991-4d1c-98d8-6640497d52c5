const fs = require('fs');
const path = require('path');

// v15.1 2023-06-05, 21:39:54 GMT
const EMOJI_VERSION = '15.1';

main();

async function main() {
  const text = await getTestFile(EMOJI_VERSION);

  console.log(`Format text to json...`);
  const collected = text
    .trim()
    .split('\n')
    .reduce(
      (accu, line) => {
        if (line.startsWith('# group: ')) {
          console.log(`  Processing ${line.substr(2)}...`);
          accu.group = line.substr(9);
          accu.grouped[accu.group] = [];
        } else if (line.startsWith('# subgroup: ')) {
          accu.subgroup = line.substr(12);
        } else if (line.startsWith('#')) {
          accu.comments = accu.comments + line + '\n';
        } else if (line.includes('unqualified') || line.includes('minimally-qualified')) {
          // Skip lines containing 'unqualified' or 'minimally-qualified
          // Do nothing, just continue to next line
        } else {
          const meta = parseLine(line);
          if (meta) {
            meta.category = `${accu.group} (${accu.subgroup})`;
            meta.group = accu.group;
            meta.subgroup = accu.subgroup;
            accu.grouped[accu.group].push(meta);
            accu.full.push(meta);
            accu.compact.push(meta.char);
          } else {
            accu.comments = accu.comments.trim() + '\n\n';
          }
        }
        return accu;
      },
      { comments: '', full: [], compact: [], grouped: {} },
    );

  console.log(`Processed emojis: ${collected.full.length}`);

  console.log('Write file: emoji.json, emoji-compact.json \n');
  await writeFiles(collected);

  console.log(collected.comments);
}

async function getTestFile(ver) {
  return new Promise((resolve, reject) => {
    try {
      const content = fs.readFileSync(path.resolve(__dirname, `${ver}.txt`), 'utf8');
      resolve(content);
    } catch (e) {
      console.error(e);
      reject(e);
    }
  });
}

function parseLine(line) {
  const data = line.trim().split(/\s+[;#] /);

  if (data.length !== 3) {
    return null;
  }

  const [codes, status, charAndName] = data;
  const [, char, name] = charAndName.match(/^(\S+) E\d+\.\d+ (.+)$/);

  return { codes, char, name };
}

const rel = (...args) => path.resolve(__dirname, ...args);

function writeFiles({ full, compact, grouped }) {
  fs.writeFileSync(rel('./emoji.json'), JSON.stringify(full), 'utf8');
  fs.writeFileSync(rel('./emoji-compact.json'), JSON.stringify(compact), 'utf8');
  fs.writeFileSync(rel('./emoji-grouped.json'), JSON.stringify(grouped), 'utf8');
}

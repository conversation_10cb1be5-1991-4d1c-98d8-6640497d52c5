<template lang="pug">
.tiptap-toolbar
  .tiptap-toolbar-controls
    .tiptap-toolbar-controls-inner.d-flex.align-items-center.align-content-center
      .d-flex.justify-content-between.flex-row.mb-1
        .d-flex.flex-row.justify-content-start.align-items-center.gap-2
          TextAlign
          LineHeight
          //- List(:class="{ disabled: isButton }")
          Link(:class="{ disabled: isButton }")
          Emoji
          DynamicText(ref="dynamicText")
        .d-flex.flex-row.justify-content-end.align-items-center.gap-2
          OmButton(
            v-if="isCopyable"
            :disabled="isUnderCopy"
            icon="paint-tool"
            iconOnly
            ghost
            small
            @click="copyStyle"
            v-tooltip="getTooltipConfig($t('tiptap.copyStyle'))"
          )
          OmButton(
            icon="copy-alt"
            iconOnly
            ghost
            small
            @click="copy"
            v-tooltip="getTooltipConfig($t('tiptap.copyElement'))"
          )
          OmButton(
            icon="trash-alt"
            iconOnly
            ghost
            small
            @click="remove"
            v-tooltip="getTooltipConfig($t('tiptap.removeElement'))"
          )
    .tiptap-toolbar-controls-inner.d-flex.align-items-center.align-content-center.gap-2
      FontFamily
      .tiptap-toolbar-separator
      FontSize
      .tiptap-toolbar-separator
      FontWeight
      .tiptap-toolbar-separator
      Color(@click="onColorClick")
      TextStyles
</template>

<script>
  import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';

  import { getStylePropertyFromType } from '@om/template-properties/src/propertyHelper/index';
  import { frameStore } from '@/util';

  import TextAlign from './tool/TextAlign.vue';
  import LineHeight from './tool/LineHeight.vue';
  import List from './tool/List.vue';
  import TextStyles from './tool/TextStyles.vue';
  import FontFamily from './tool/FontFamily.vue';
  import FontSize from './tool/FontSize.vue';
  import FontWeight from './tool/FontWeight.vue';
  import Color from './tool/Color.vue';
  import Link from './tool/Link.vue';
  import Emoji from './tool/Emoji.vue';
  import DynamicText from './tool/DynamicText.vue';
  import { getStyles } from './helpers/getStyles';

  export default {
    name: 'TipTapToolbar',
    components: {
      TextAlign,
      LineHeight,
      List,
      TextStyles,
      FontFamily,
      FontSize,
      FontWeight,
      Color,
      Link,
      Emoji,
      DynamicText,
    },
    data: () => ({
      isUnderCopy: false,
      linkActive: false,
    }),
    computed: {
      ...mapState(['selectedElement']),
      ...mapState('wysiwyg', ['visible', 'activeElement']),
      ...mapGetters('wysiwyg', ['isVisible']),
      isCopyable() {
        return getStylePropertyFromType(this.selectedElement.type)?.length;
      },
      isButton() {
        return this.selectedElement.type === 'OmButton';
      },
    },
    watch: {
      activeElement(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.$children.forEach((child) => child?.init?.());
        }
      },
      'selectedElement.data.text': {
        handler() {
          this.$nextTick(() => {
            this.updateChildren();
          });
        },
        deep: true,
      },
    },
    mounted() {
      this.isUnderCopy = false;
      frameStore().state.clipboardStyle = {};

      // Store event handlers as instance methods to ensure proper cleanup
      this.updateElementStyleHandler = () => {
        if (this.isUnderCopy) {
          frameStore().state.clipboardStyle = {};
          this.isUnderCopy = false;
        }
      };

      this.wysiwygUpdateHandler = () => {
        this.$nextTick(() => {
          this.updateChildren();
        });
      };

      this.dynamicTextHandler = ({ command, value }) => {
        window.top._editor.chain()[command](value).run();
      };

      this.$bus.$on('updateElementStyle', this.updateElementStyleHandler);
      this.$children.forEach((child) => {
        child.$on('editorCommand', ({ command, value }) => {
          if (command === 'setLineHeight') {
            window.top._editor
              .chain()
              [command](...value)
              .run();
          } else {
            window.top._editor.chain().focus()[command](value).run();
          }
        });
      });
      this.$bus.$on('wysiwygUpdate', this.wysiwygUpdateHandler);
      this.$bus.$on('dynamicText', this.dynamicTextHandler);
      this.$emit('mounted', this);
    },
    beforeDestroy() {
      this.$bus.$off('resetInnerMarks');
      this.$bus.$off('updateElementStyle', this.updateElementStyleHandler);
      this.$bus.$off('wysiwygUpdate', this.wysiwygUpdateHandler);
      this.$bus.$off('dynamicText', this.dynamicTextHandler);
    },
    methods: {
      ...mapMutations(['deselectAll']),
      ...mapActions('wysiwyg', ['hideWysiwyg']),
      copyStyle() {
        const { href, target, rel, ...styles } = getStyles();
        this.isUnderCopy = true;
        frameStore().commit('copyElementStyle', { mergedStyles: styles, tiptap: true });
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'buttonClicked',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            buttonLabel: 'copy-style',
          },
        });
      },
      copy() {
        frameStore().dispatch('copyElement', this.selectedElement.uid);
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'buttonClicked',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            buttonLabel: 'copy-element',
          },
        });
      },
      remove() {
        frameStore().commit(
          'selectElementByUid',
          this.selectedElement.pageId ?? this.selectedElement.uid,
        );
        this.$nextTick(() => {
          this.deselectAll();
        });
        frameStore().dispatch('removeElement', { uid: this.selectedElement.uid });
        this.$bus.$emit('reportEditorEvent', {
          interactionType: 'buttonClicked',
          location: 'toolbar',
          extras: {
            elementType: this.selectedElement.type,
            buttonLabel: 'remove-element',
          },
        });
      },
      updateChildren() {
        this.$children.forEach((child) => child?.updateSelected?.());
      },
      onColorClick() {
        this.hideChildrenMenu();
      },
      hideChildrenMenu() {
        this.$children.forEach((child) => {
          if (child?.$el?.classList?.contains?.('ds-dropdown-menu')) {
            child?.$children?.[0]?.hideMenu?.();
          }
        });
      },
      getTooltipConfig(content) {
        return {
          content,
          trigger: 'hover',
          placement: 'top',
          popover: { arrow: false },
          delay: { show: 350, hide: 0 },
          classes: 'ds-dropdown-menu-tooltip',
        };
      },
    },
  };
</script>

<style lang="sass">
  @import '@/sass/variables/_colors'

  .tiptap-toolbar
    // Don't remove this hack 0px will be transpiled to 0 which is not valid value in max()
    --zero-px: calc(10px - 10px)
    z-index: 9999998
    position: absolute
    top: max(var(--zero-px), min(var(--toolbar-top), var(--workspace-height)))
    left: max(72px, calc(var(--toolbar-left) - var(--toolbar-width) / 2))
    background: white
    padding: 8px
    border: 1px solid $om-gray-300
    border-radius: 8px
    box-shadow: 0px 0px 32px 0px #0000001A, 0px 8px 20px 0px #00000033
    &-separator
      display: inline-block
      height: 32px
      width: 1px
      background: $om-gray-300
      margin-inline: 8px
    .gap-2
      gap: 4px
    .ds-dropdown-menu
      font-size: 16px
      .ds-dropdown-menu-trigger
        padding: 4px 3px
        max-height: 32px
      .ds-dropdown-menu-item
        padding: 4px 8px
        span
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis
      .ds-dropdown-menu--icon .ds-dropdown-menu-trigger
        padding-right: 4px
        padding-left: 12px
      ::v-deep .btn.btn-add-icon
        padding-left: 3px
        padding-right: 3px
      .ml-1
        margin-left: 4px
      .ds-dropdown-menu--horizontal
        .ds-dropdown-menu-content
          gap: 2px
        .ds-dropdown-menu-item
          padding: 4px
          &[title="Strike"],
          &[title="Áthúzás"]
            padding: 0
</style>

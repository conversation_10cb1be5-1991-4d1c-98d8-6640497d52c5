<template lang="pug">
element-resize(
  v-if="!isSSR"
  :uid="item.uid"
  elementType="OmText"
  disableHeightResize
  @element-resize="$emit('element-resize', $event)"
)
  component(
    :is="editorComponent"
    :item="item"
    elementClass="om-text"
    :extraClass="item.desktop.textJustify"
  )
component(
  v-else=""
  :is="editorComponent"
  :item="item"
  elementClass="om-text"
  :extraClass="item.desktop.textJustify"
)
</template>

<script>
  import { mapGetters } from 'vuex';
  import ElementResize from '@/editor/components/visualizers/ElementResize.vue';

  export default {
    components: {
      ElementResize,
      TipTap: () => import('@/editor/components/wysiwyg/tiptap/Editor.vue'),
    },

    props: ['item', 'isSSR'],

    computed: {
      ...mapGetters(['isEditorTipTapTextEnabled']),
      editorComponent() {
        // use the previous quill-editor component
        return this.isEditorTipTapTextEnabled &&
          !window.parent.location.href?.includes('/template-editor/')
          ? 'TipTap'
          : 'quill-editor';
      },
    },
  };
</script>

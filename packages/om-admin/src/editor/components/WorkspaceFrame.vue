<template lang="pug">
.om-workspace-inner.om-editor(
  :class="{ 'om-position-set': workspacePositionClass }"
  :lang="$i18n.locale"
)
  link(:href="frontendCssUrl" rel="stylesheet")
  #loadComplete(style="display: none" v-if="loadComplete")
  .om-editor-center(
    :class="{ 'om-mobile-editor': mobilePreview, 'om-teaser-page': isTeaser }"
    @click="onOuterClick"
  )
    om-workspace-wrapper(:style="workspaceWrapperHeight" @click.stop)
      template(slot="top")
        .om-workspace-top(v-if="!docker")
          .om-editor-top(v-if="editMode")
            quill-toolbar.animated.fadeInDown(@dblclick.native.stop="")
      template(slot="center")
        .om-workspace-center(
          ref="workspaceCenter"
          @click="overlayClickHandler($event)"
          :class="{ 'editor-mode': editMode, 'om-large': isCanvasLarge, 'om-wheel-editor': hasWheel, dragging: dragging, [`dragging-${dragInfo && dragInfo.type}`]: dragging }"
          :style="{ 'max-height': workspaceMaxHeight }"
          @mouseleave="workspaceLeaving()"
        )
          EditorMock
          iframe(style="display: none")
          .om-workspace-center-before
          .om-holder
            .om-container
              .om-middle
                .om-iframe-container
                  #om-campaign-0.om-workspace-content(
                    ref="wsContent"
                    @animationend="onWsContentAnimationEnd"
                    :class="{ embedded: isEmbedded, 'teaser-turned-off': isTeaserAndDisabled, 'mobile-preview': mobilePreview, 'is-fullscreen': isFullscreen || isEmbedded }"
                  )
                    .teaser-hidden(v-if="isTeaserAndDisabled")
                      img(:src="require('@/assets/editor/svg/TeaserHidden.svg')")
                      span {{ $t('teaserDisabledWarning') }}
                    .om-asset-helper(
                      v-if="docker"
                      style="display: none"
                      :data-fonts="JSON.stringify(fonts.realUsed)"
                      :data-custom-fonts="JSON.stringify(fonts.custom)"
                      :data-images="JSON.stringify(imageSrcsOnFirstPage)"
                    )
                    .om-tab-wrapper-v2(
                      v-if="hasTemplateFeature(TEMPLATE_FEATURES.NEW_TEASER) && !teaserPreview"
                      v-show="(selectedPage && selectedPage.uid === teaserPage.uid)"
                      :class="[tabSwitchAnimationClass, tabPositionClass]"
                      :data-before-popup="teaserPage.data.display.before ? 1 : 0"
                      :data-after-page-load-value="teaserPage.data.display.afterPageLoadValue"
                      :data-after-value="teaserPage.data.display.afterValue"
                      :data-on-close="teaserPage.data.display.after ? 1 : 0"
                      :data-teaser-device="teaserPage.data.display.deviceDisplay"
                      :data-switch-animation="tabSwitchAnimation"
                      :data-tab-position="teaserPage.data.position"
                      :data-tab-position-mobile="teaserPage.data.mobilePosition"
                      :data-attention-seeker-type="teaserPage.data.attentionSeeker.type"
                      :data-attention-seeker-freq="teaserPage.data.attentionSeeker.freq"
                      :data-permanent="isPermanentTeaser ? 1 : 0"
                      @mouseover="pageHovered = true"
                      @mouseleave="pageHovered = false"
                      @click="outerCanvasClickHandler()"
                    )
                      .om-tab-animation(ref="omTabAnimation")
                        om-canvas-v2(
                          v-if="teaserPage"
                          :pageObject="teaserPage"
                          :index="0"
                          :dragging.sync="dragging"
                          :pageHovered.sync="pageHovered"
                        )
                    .om-tab-wrapper(
                      v-else
                      v-show="teaserPreview"
                      ref="tabWrapper"
                      :class="[cornerClass, ...tabSwitchAnimationClass]"
                      @click.stop="tabClick"
                      :data-before-popup="tabBefore"
                      :data-on-close="tabOnClose"
                      :data-tab-position="tabPosition"
                      :data-switch-animation="tabSwitchAnimation"
                      :data-attention-seeker-type="tabAttentionSeekerType"
                      :data-attention-seeker-freq="tabAttentionSeekerFreq"
                    )
                      .om-tab-animation(ref="omTabAnimation")
                        .om-tab.flex-row-justify-content.editor-ele_mBbPvb61Q
                          teaser-quill(:selected="teaserSelected")
                    .om-overlay(
                      v-if="!showAdminLoader"
                      ref="overlay"
                      :id="inEditor ? 'editor-mode' : ''"
                      :class="overlayClasses"
                      :data-tab="isTabActive ? 1 : null"
                      :style="{ display: (!inEditor && tabBefore) || (inEditor && (teaserPreview || (selectedPage && teaserPage && selectedPage.uid === teaserPage.uid))) ? 'none' : null, background: inEditor && isSidebar && !mobilePreview && !withOverlay ? 'none !important' : undefined }"
                      :data-animation="backgroundAnimationSettings"
                      :data-background-color="overlayBackgroundColor"
                    )
                      .om-overlay-center(
                        ref="overlayCenter"
                        :class="animationClasses"
                        @drop="floatingImageDrop($event, true)"
                        @dragover.prevent=""
                        :style="{ alignItems: shouldAlignFlexStart && mobilePreview && !isNano && !isFullscreen && !hasWheel ? 'flex-start' : '' }"
                      )
                        OverlayAnimation(
                          v-if="!isSidebar && !isNano && overlayAnimation && overlayAnimation.type"
                          :config="overlayAnimation"
                        )
                        component(
                          :is="backgroundAnimationName"
                          v-if="inEditor && backgroundAnimation && backgroundAnimationOverlay"
                          :itemCount="backAnimItemCount"
                        )
                        .om-outer-canvas(
                          @dblclick.stop=""
                          :class="[{ 'editor-mode': editMode, 'om-large': isCanvasLarge }]"
                          :style="{ position: editMode ? 'relative' : null }"
                          @drop.stop=""
                          @drag.stop=""
                          @mouseenter="pageHovered = true"
                          @mouseleave="pageHovered = false"
                          @click="outerCanvasClickHandler()"
                          ref="outerCanvas"
                        )
                          template(v-for="(page, pageIndex) in templatePages")
                            om-canvas-v2(
                              :pageObject="page"
                              :pageIndex="pageIndex"
                              :dragging.sync="dragging"
                              :pageHovered.sync="pageHovered"
                            )
                      made-by-om
</template>

<script>
  import hotkeys from 'hotkeys-js';
  import { debounce, get as _get, isEmpty } from 'lodash-es';
  import { mapActions, mapGetters, mapMutations, mapState, createNamespacedHelpers } from 'vuex';

  import Snowing from '@/editor/components/animations/Snowing.vue';
  import MadeByOm from '@/editor/components/MadeByOm.vue';
  import OverlayAnimation from '@/editor/components/animations/Overlay.vue';
  import AnimationNone from '@/editor/components/animations/None.vue';
  import OmWorkspaceWrapper from '@/editor/components/WorkspaceWrapper.vue';
  import TeaserQuill from '@/editor/components/sidebar/inputs/TeaserQuill.vue';
  import OmCanvas from '@/editor/components/Canvas';
  import OmCanvasV2 from '@/editor/components/CanvasV2';
  import EditorMock from '@/editor/components/EditorMock';

  import { CAMPAIGN_EVENT_TYPES, EMBEDDED_CAMPAIGN_EVENT_TYPES } from '@/config/constants';
  import {
    appendScript,
    disableLinkClicks,
    getQueryVariable,
    TEMPLATE_FEATURES,
    getCustomJsByEvent,
  } from '@/editor/util';
  import { replaceCss } from '@/editor/services/CssHelper';
  import font from '@/mixins/font';
  import { attentionSeekers, tabAnimations } from '@/editor/config/animation';
  import { loadAdditionalFonts, loadUsedFonts } from '@/utils/fontLoad';
  import { MockLayouts } from '@/editor/components/MockSite.vue';
  import backgroundAnimationMixin from '@/editor/mixins/backgroundAnimation';
  import { objectHash } from '@/utils/objectHash';
  import runtimeConfig from '@/config/runtime';
  import productInitializer from '@om/editor-ssr-shared/src/core/initializers/v2/post/OmProduct';
  import floatingImageMixin from '@/editor/mixins/floatingImage';
  import teaserMixin from '@/editor/mixins/teaser';
  import LogRocket from 'logrocket';
  import { track } from '@/services/xray';
  import { isPage } from '../../util';

  const { mapGetters: customThemeGetters, mapMutations: customThemeMutations } =
    createNamespacedHelpers('customTheme');

  const frontendUrl = runtimeConfig.VUE_APP_FRONTEND;
  const channel = window.BroadcastChannel ? new BroadcastChannel('om-clipboard-style') : null;
  const LARGE_CANVAS_THRESHOLD_EM = 44;
  const _clone = (v) => JSON.parse(JSON.stringify(v));
  const _emit = (bus, eventName, value) => {
    if (bus) bus.$emit(eventName, value);
  };
  const _replaceCustomCss = (css, inDocker) => replaceCss(css || '', true, inDocker);
  let globalID;

  const _sendPage = (vueComp) => {
    const { uid, style, data } = vueComp.page;

    vueComp?.$bus?.$emit?.('sendPage', _clone({ uid, style, data }));
  };

  export default {
    components: {
      'animation-snowing': Snowing,
      'animation-false': AnimationNone,
      TeaserQuill,
      OverlayAnimation,
      OmWorkspaceWrapper,
      OmCanvas,
      OmCanvasV2,
      EditorMock,
      MadeByOm,
    },

    mixins: [font, backgroundAnimationMixin, floatingImageMixin, teaserMixin],

    data() {
      return {
        MockLayouts,
        isCanvasLarge: false,
        userGivenUrl: null,
        elementAdjacentCount: 0,
        showEmptyCanvasDropzone: null,
        jsInserted: false,
        pageHovered: false,
        dragging: false,
        TEMPLATE_FEATURES,
        editorPagesExpanded: false,
        styleFromOtherTab: false,
        isRightSidebarOpen: false,
        shouldAlignFlexStart: false,
        outerCanvasObserver: null,
        wsContentObserver: null,
      };
    },

    computed: {
      ...mapState([
        'campaign',
        'dragInfo',
        'dropLocation',
        'editMode',
        'loadComplete',
        'mobilePreview',
        'quillToolbarInfo',
        'selectedColumnId',
        'showAdminLoader',
        'template',
        'templateSaveData',
        'usedFonts',
        'realUsedFonts',
        'docker',
        'fonts',
        'teaserPreview',
        'hasFeedbackOnPage',
        'imageSrcsOnFirstPage',
        'teaserSelected',
        'databaseId',
        'workspaceFontsLoaded',
        'maxPageHeight',
        'selectedElement',
        'selectedColumn',
        'selectedRow',
        'selectedPage',
        'clipboardStyle',
        'hasCouponOnPage',
      ]),
      ...mapGetters([
        'boxes',
        'isInterstitial',
        'isFullscreen',
        'isNano',
        'isEmbedded',
        'isSidebar',
        'hasWheel',
        'page',
        'pageCount',
        'rowIds',
        'installedFonts',
        'mockSiteLayout',
        'isNewWheelLayout',
        'templateHash',
        'inEditor',
        'teaserPage',
        'hasTemplateFeature',
        'hasAccountFeature',
        'isEditorTipTapButtonEnabled',
        'isEditorTipTapTextEnabled',
      ]),
      ...customThemeGetters(['getElementsByUid']),
      isTeaserAndDisabled() {
        if (!this.isNewTeaser) return false;

        return (
          (this.teaserPreview || this.selectedPage?.uid === this.teaserPage?.uid) &&
          !this.isTeaserEnabled
        );
      },
      isTeaser() {
        return this.selectedPage?.isTeaser || false;
      },
      nanoClass() {
        const desktopPosition = this.template.style.overlay.position;
        const mobilePosition = this.template.style.overlay.mobilePosition;
        if (this.isNano) {
          if (desktopPosition === 1 && !mobilePosition) return 'top';
          if (desktopPosition === 7 && !mobilePosition) return 'bottom';
          if (desktopPosition === 1 && mobilePosition)
            return `top nano-mobile-pos-${mobilePosition}`;
          if (desktopPosition === 7 && mobilePosition)
            return `bottom nano-mobile-pos-${mobilePosition}`;
        }
        return '';
      },
      workspaceWrapperHeight() {
        return {
          'max-height': this.workspaceWrapperMaxHeight,
        };
      },
      animation() {
        return this.template.style.animation.type;
      },
      overlayAnimation() {
        return this.template.style.overlay.animation;
      },
      animationClasses() {
        const prefix = !this.editMode ? 'om-' : '';
        const classes = [`${prefix}animated`];

        classes.push(`${prefix}${this.animation}`);

        return classes;
      },
      cornerClass() {
        const value = _get(this.template, 'style.tab.type.style');
        if (value === 'corner') {
          return 'om-tab-corner';
        }
        if (value === 'basic') {
          return 'om-revers-overflow om-tab-basic';
        }

        return 'om-revers-overflow';
      },
      tabSwitchAnimation() {
        if (this.hasTemplateFeature(TEMPLATE_FEATURES.NEW_TEASER)) {
          const position = this.teaserPage.data.position;

          if (['top-left', 'top-center', 'top-right', 'left-center'].includes(position))
            return 'fadeInDown';

          return 'fadeInUp';
        }

        return this.template.style.tab.animation.switch.type;
      },
      tabAttentionSeekerType() {
        return this.template.style.tab.animation.attentionSeeker.type;
      },
      tabAttentionSeekerFreq() {
        return this.template.style.tab.animation.attentionSeeker.freq;
      },
      tabSwitchAnimationClass() {
        if (this.editMode && this.tabSwitchAnimation) {
          return ['animated', this.tabSwitchAnimation];
        }
        return [];
      },
      tabPositionClass() {
        return `tab-position-${this.teaserPage.data.position}`;
      },
      overlayPosition() {
        return this.template.style.overlay.position;
      },
      workspacePositionClass() {
        const position = this.overlayPosition;
        return this.editMode && position && position !== 5;
      },
      positionClass() {
        if (this.isNano) return;
        if (!this.isSidebar) return;
        let posClass;
        switch (this.overlayPosition) {
          case 1:
            posClass = 'om-top-left';
            break;
          case 2:
            posClass = 'om-top-center';
            break;
          case 3:
            posClass = 'om-top-right';
            break;
          case 4:
            posClass = 'om-middle-left';
            break;
          // case 5: posClass = 'om-middle-center'; break
          case 6:
            posClass = 'om-middle-right';
            break;
          case 7:
            posClass = 'om-bottom-left';
            break;
          case 8:
            posClass = 'om-bottom-center';
            break;
          case 9:
            posClass = 'om-bottom-right';
            break;
        }
        return `om-sidebar ${posClass}`;
      },
      fitToScreen() {
        return this.template.style.overlay.fitToScreen ? 'om-overlay-fit-to-screen' : '';
      },
      withOverlay() {
        return this.fitToScreen && this.template.style.overlay.withOverlay
          ? 'om-fit-to-screen-with-overlay'
          : '';
      },
      withoutOverlayOnMobile() {
        return this.template.style.overlay.withoutOverlayOnMobile
          ? 'om-without-overlay-mobile'
          : '';
      },
      fitToScreenOnMobile() {
        return this.template.style.overlay.fitToScreenOnMobile
          ? 'om-overlay-fit-to-screen-mobile'
          : '';
      },
      tabPosition() {
        return this.template.style.tab.position;
      },
      tabDisplay() {
        return this.template.style.tab.display;
      },
      tabV2Display() {
        return this.teaserPage.data.display;
      },
      isTabActive() {
        if (this.hasTemplateFeature(TEMPLATE_FEATURES.NEW_TEASER)) {
          return this.tabV2Display.before || this.tabV2Display.after;
        }

        return this.tabBefore || this.tabOnClose;
      },
      tabBefore() {
        return this.tabDisplay.before ? 1 : 0;
      },
      afterPageLoadValue() {
        return this.tabDisplay.afterPageLoadValue;
      },
      afterValue() {
        return this.tabDisplay.afterValue;
      },
      tabOnClose() {
        return this.tabDisplay.after ? 1 : 0;
      },
      isPermanentTeaser() {
        return this.template.data.isPermanentTeaser;
      },
      isPageViewBasedPackage() {
        return window.parent.om.store.getters['payment/isPageViewBasedPackage'];
      },
      fonts() {
        const fonts = {
          google: [],
          custom: [],
          realUsed: [],
        };

        this.installedFonts.forEach((installed) => {
          if (installed.custom && this.usedFonts.includes(installed.key)) {
            fonts.custom.push(installed.family);
          } else if (this.usedFonts.includes(installed.key)) {
            if (this.realUsedFonts[installed.key]) {
              let realF = `${installed.family.replace(/ /g, '+')}`;
              if (installed.weights && installed.weights.length) {
                const collectedWeights = Object.keys(this.realUsedFonts[installed.key]);
                const hasMissingFontWeight = collectedWeights.some(
                  (weight) => !installed.weights.includes(weight),
                );
                let weightsString;

                if (hasMissingFontWeight) {
                  weightsString = `${installed.weights.join(',')}`;
                } else {
                  weightsString = `${collectedWeights.join(',')}`;
                }
                realF += `:${weightsString}`;
              }
              if (installed.installedSubsets)
                realF += `${
                  !installed.weights || !installed.weights.length ? ':' : ''
                }:${installed.installedSubsets.join(',')}`;
              fonts.realUsed.push(realF);
            }
            let f = `${installed.family.replace(/ /g, '+')}`;
            if (installed.weights && installed.weights.length)
              f += `:${installed.weights.join(',')}`;
            if (installed.installedSubsets)
              f += `${
                !installed.weights || !installed.weights.length ? ':' : ''
              }:${installed.installedSubsets.join(',')}`;
            fonts.google.push(f);
          }
        });
        return fonts;
      },
      frontendCssUrl() {
        return `${frontendUrl}/assets/css/om.base.css?v=${Date.now()}`;
      },
      overlayBackgroundColor() {
        return this.template.style.overlay.backgroundColor;
      },
      templatePages() {
        if (!this.template) return [];
        const elements = this.template.elements || [];
        return elements.filter((e) => isPage(e));
      },
      mobileOverlayPositionClass() {
        const position = this.template.style.overlay.mobilePosition;
        return `om-overlay-mobile-${position}`;
      },
      overlayClasses() {
        const classes = [
          this.mobilePreview ? 'om-mobile-preview' : '',
          this.positionClass,
          this.isNano ? 'nanobar' : '',
          this.nanoClass,
          this.isInterstitial ? 'interstitial' : '',
          this.isFullscreen ? 'om-fullscreen' : '',
          this.hasWheel ? 'om-wheel' : '',
          this.isNewWheelLayout ? 'om-wheel-position-bottom' : '',
          this.isEmbedded ? 'om-embedded' : '',
          this.fitToScreen,
          this.withOverlay,
          this.withoutOverlayOnMobile,
          this.fitToScreenOnMobile,
          this.mobileOverlayPositionClass,
        ];

        if (this.template && this.template.__version) {
          const versionClass = `om-template-version-${this.template.__version.replace(/\./g, '-')}`;
          classes.push(versionClass);
        }

        return classes;
      },
      workspaceMaxHeight() {
        return this.calculateMaxHeight(6.5, 11.5);
      },
      workspaceWrapperMaxHeight() {
        return this.calculateMaxHeight(5.75, 10.75);
      },
    },
    watch: {
      clipboardStyle: {
        handler(style) {
          if (isEmpty(style)) {
            this.setCursorIcon('remove');
            if (channel) {
              channel.postMessage({});
            }
          } else if (!this.styleFromOtherTab) {
            this.setCursorIcon('add');
            if (channel) {
              channel.postMessage({ style });
            }
          } else if (this.styleFromOtherTab && !isEmpty(style)) {
            this.setCursorIcon('add');
            this.styleFromOtherTab = false;
          }
        },
        deep: true,
      },
      isRightSidebarOpen: {
        handler(newVal) {
          if (newVal) {
            this.toggleRightSidebar(true);
          }
        },
        immediate: true,
      },
      'template.elements': {
        handler() {
          if (this.loadComplete && this.$bus) {
            this.$bus?.$emit?.('historySave');
          }
        },
        deep: true,
        immediate: true,
      },
      isEmbedded() {
        this.$nextTick(() => {
          this.setSpecialElementsOnPage();
        });
      },
      template: {
        handler: debounce(function handle() {
          if (this.$store.state.loadComplete) {
            this.calculateTemplateHash();
          }
        }, 200),
        deep: true,
      },
      'template.style': {
        handler() {
          if (this.$bus) this.$bus?.$emit?.('generateCss');
          else replaceCss(this.template, false, this.docker);
        },
        deep: true,
      },
      'template.palette': {
        handler() {
          this.regenerateBaseStyles();
          this.regenerateElementStyles();
        },
        deep: true,
      },
      selectedPage: {
        handler(newValue, oldValue) {
          _emit(this.$bus, 'setSelectedPage', newValue);

          if (newValue && (!oldValue || oldValue.uid !== newValue.uid)) {
            this.$nextTick(() => {
              this.attachCanvasResizeSenzor(newValue.uid);
            });
          }
        },
        immediate: true,
      },
      selectedElement: {
        handler(newValue) {
          if (newValue) {
            this.$bus?.$emit?.('setSelectedElement', newValue);
            if (newValue.type !== 'OmPage') {
              this.setCursorIcon('remove');
              this.resetCopyElementStyle();
            }
          }
        },
        immediate: true,
      },
      selectedColumn: {
        handler(newValue) {
          if (newValue) {
            this.$bus?.$emit?.('setSelectedColumn', newValue);
          }
        },
        immediate: true,
      },
      selectedRow: {
        handler(newValue) {
          if (newValue) {
            this.$bus.$emit('setSelectedRow', newValue);
          }
        },
        immediate: true,
      },
      templateSaveData(v) {
        this.loadTemplateWeb(v);
      },
      mobilePreview(v) {
        this.setFloatingActionsPosition();
        this.$nextTick(() => {
          this.setFloatingActionsPosition();
          disableLinkClicks();
          this.$refs.workspaceCenter.style.maxHeight = this.workspaceMaxHeight;
          if (!v) {
            window.parent.document.querySelector('.om-workspace-container').scrollTo(0, 0);
          }
        });
      },
      'campaign.domain': function watchDomain() {
        this.template.elements.forEach((element) => {
          if (element.type === 'OmProduct') {
            productInitializer({ element, template: this.template });
          }
        });
      },
      'template.customCss': function watchCustom(v) {
        _emit(this.$bus, 'setCustomCss', v);
      },
      pageCount(value) {
        _emit(this.$bus, 'setEditorStateAttr', { attr: 'pageCount', value });
      },
      'quillToolbarInfo.show': function watchShow(v) {
        if (v === true) {
          this.$nextTick(() => {
            this.repositionQuill();
          });
        }
      },
      'template.style.tab.type.size': function watchSize() {
        if (this.teaserSelected) {
          this.$nextTick(() => {
            this.repositionQuill();
          });
        }
      },
      installedFonts(n) {
        if (!n.length) return;
        if (!this.workspaceFontsLoaded) loadUsedFonts({ store: this.$store, bus: this.$bus });
        else if (!this.docker) loadAdditionalFonts({ store: this.$store });
      },
      tabAttentionSeekerType(v) {
        if (v) {
          this.replayTeaserAttentionSeeker();
        }
      },
      tabPosition(v) {
        if (v) {
          this.setFloatingActionsPosition();
          this.replayTeaserAnimation();
          if (this.teaserSelected) {
            this.$nextTick(() => {
              this.repositionQuill();
            });
          }
        }
      },
      'teaserPage.data.position': function watchPosition(v) {
        if (v) {
          this.setFloatingActionsPosition();
        }
      },
      'teaserPage.data.attentionSeeker.type': function watchType(v) {
        if (v) {
          this.replayTeaserAttentionSeeker();
        }
      },
      animation(v) {
        if (v && this.$refs.overlayCenter) {
          this.addOverflowForAnimation(this.$refs.overlayCenter);
        }
      },
      tabSwitchAnimation(v) {
        if (v && this.$refs.tabWrapper) {
          this.addOverflowForAnimation(this.$refs.tabWrapper);
        }
      },
      'template.style.tab.type.style': function watchStyle() {
        if (this.teaserSelected) {
          this.$nextTick(() => {
            this.repositionQuill();
          });
        }
      },
      overlayPosition() {
        this.setFloatingActionsPosition();
      },
    },
    created() {
      const baseTemplate = getQueryVariable('baseTemplate');
      const variantId = getQueryVariable('variantId');
      const docker = getQueryVariable('docker');
      const token = getQueryVariable('previewToken');
      if (docker) this.setStateAttr({ attr: 'docker', value: true });
      if (token) {
        this.setStateAttr({ attr: 'previewTokenSet', value: true });
        localStorage.omEditorToken = token;
      }
      if (baseTemplate || variantId) {
        this.setStateAttr({ attr: 'editMode', value: false });
        if (baseTemplate) this.loadTemplateWeb({ type: 'base', value: baseTemplate });
        if (variantId) this.loadTemplateWeb({ type: 'variant', value: variantId });
      }

      hotkeys('ctrl+z, cmd+z', () => {
        _emit(window.parent.om.bus, 'historyUndo');
        return false;
      });

      hotkeys('ctrl+y, cmd+shift+z, cmd+y', () => {
        _emit(window.parent.om.bus, 'historyRedo');
        return false;
      });

      this.$bus?.$on?.('enterPageElementOnLayers', () => {
        this.pageHovered = true;
      });

      this.$bus?.$on?.('leavePageElementOnLayers', () => {
        this.pageHovered = false;
      });
    },

    destroyed() {
      hotkeys.unbind('ctrl+z, cmd+z');
      hotkeys.unbind('ctrl+y, cmd+shift+z, cmd+y');
    },

    beforeUpdate() {
      if (this.$bus) return; // In edit mode...

      if (
        this.docker &&
        !this.jsInserted &&
        ((!this.isEmbedded && this.$refs.overlay) ||
          (this.isEmbedded && this.$refs.overlay.classList.value.includes('om-embedded')))
      ) {
        this.jsInserted = this.appendCustomScripts();
      }
      if (this.docker && this.template.customCss)
        _replaceCustomCss(this.template.customCss, this.docker);
    },

    mounted() {
      if (channel) {
        channel.addEventListener('message', (event) => {
          const { style } = event.data;
          if (!style) {
            this.styleFromOtherTab = false;
            this.setCursorIcon('remove');
            return;
          }
          this.$bus?.$emit?.('copyElementStyle', style);
          this.styleFromOtherTab = true;
        });
      }
      if (!this.$bus) return;
      this.$bus?.$on?.('replaceTemplate', this.replaceTemplate);
      this.$bus?.$on?.('addElement', this.addElement);
      this.$bus?.$on?.('removeElement', this.removeElement);
      this.$bus?.$on?.('switchInputElement', this.switchInputElement);
      this.$bus?.$on?.('addImages', this.addImages);
      this.$bus?.$on?.('addFields', this.addFields);
      this.$bus?.$on?.('addCustomField', this.addCustomField);
      this.$bus?.$on?.('changeLocale', (locale) => {
        if (this.$i18n) {
          this.$i18n.locale = locale;
        }
      });
      this.$bus?.$on?.('setDragInfo', (params) => {
        if (params.drag) {
          this.dragging = true;
        }
        this.setDragInfo(params);
      });
      this.$bus?.$on?.('copyElementStyle', (params) => {
        this.copyElementStyle(params);
      });
      this.$bus?.$on?.('deselectAll', this.deselectAll);
      this.$bus?.$on?.('updateStyle', this.updateStyle);
      this.$bus?.$on?.('updateData', this.updateData);
      this.$bus?.$on?.('updateSelectedRow', this.updateSelectedRow);
      this.$bus?.$on?.('updateColumnCount', this.updateColumnCount);
      this.$bus?.$on?.('updateTemplateStyle', this.updateTemplateStyle);
      this.$bus?.$on?.('requestGlobalStyle', () => {
        _emit(this.$bus, 'sendGlobalStyle', _clone(this.template.style));
        _emit(this.$bus, 'setEditorStateAttr', {
          attr: 'images',
          value: _clone(this.template.images || []),
        });
        _emit(this.$bus, 'setEditorStateAttr', {
          attr: 'inputs',
          value: _clone(this.template.inputs || []),
        });
        _emit(this.$bus, 'setEditorStateAttr', {
          attr: 'wheelOptions',
          value: _clone(this.template.wheelOptions || []),
        });
        _emit(this.$bus, 'setEditorStateAttr', {
          attr: 'scratchCardOptions',
          value: _clone(this.template.scratchCardOptions || []),
        });
        _emit(this.$bus, 'generateCss');
      });
      this.$bus?.$on?.('requestPage', () => _sendPage(this));
      this.$bus?.$on?.('generateCss', () => {
        replaceCss(this.template, false, this.docker);
      });
      this.$bus?.$on?.('updateElementStyle', (uid) => {
        this.$store.dispatch('updateElementStyle', uid);
      });
      this.$bus?.$on?.('updateElementStyle', () => {
        // fallback for teaser
        const uid = this.selectedElement?.uid || this.selectedPage.uid;
        const isModified = this.getElementsByUid(uid);

        if (!isModified) {
          this.modifyElementByUid({ uid, value: true });
        }
      });
      this.$bus?.$on?.('setPreviewCss', (data) => _replaceCustomCss(data, this.docker));
      this.$bus?.$on?.('setCustomCss', (data) => _replaceCustomCss(data, this.docker));
      this.$bus?.$on?.('resetCustomCss', () =>
        _replaceCustomCss(this.template.customCss, this.docker),
      );
      this.$bus?.$on?.('removePage', this.removePage);
      this.$bus?.$on?.('movePage', this.movePage);
      this.$bus?.$on?.('copyPage', (sourcePageId) => this.copyPage({ uid: sourcePageId }));
      this.$bus?.$on?.('addPage', this.addPage);
      this.$bus?.$on?.('selectPage', (pageId) => {
        this.addOverflowForAnimations();
        this.selectPage(pageId);
        _sendPage(this);
      });
      this.$bus?.$on?.('selectedRowFromEditor', this.setSelectedRow);
      this.$bus?.$on?.('selectElementByUid', (uid) => this.selectElementByUid({ uid }));
      this.$bus?.$on?.('setMobilePreview', this.setMobilePreview);
      this.$bus?.$on?.('validateElements', this.validateElements);
      this.$bus?.$on?.('saveTemplate', this.saveTemplateWeb);
      this.$bus?.$on?.('loadTemplate', this.loadTemplateWeb);
      this.$bus?.$on?.('setWorkspaceStateAttr', this.setStateAttr);
      this.$bus?.$on?.('selectElement', (uid) => this.selectElement({ uid }));
      this.$bus?.$on?.('updatePageTitle', this.updatePageTitle);
      this.$bus?.$on?.('setTeaserPreview', (payload) => {
        this.addOverflowForAnimations();

        this.setTeaserPreview(payload);
      });
      document.addEventListener('keyup', (e) => {
        if (e.keyCode === 27) {
          this.resetEditorPane();
        }
      });
      disableLinkClicks();
      this.$bus?.$on?.('workspaceFontsLoaded', () => {
        // this.$bus?.$emit?.('showAdminLoader', false)

        this.setStateAttr({ attr: 'workspaceFontsLoaded', value: true });

        this.$nextTick(() => {
          this.$bus?.$emit?.('historySave');
          this.postInitElements();
          this.$bus?.$emit?.('generateCss');
        });
      });
      this.$bus?.$on?.('getFonts', this.getFonts);
      this.$bus?.$on?.('setElementDefaults', this.setElementDefaults);
      this.$bus?.$on?.('setThemeStyle', this.setThemeStyle);
      this.$bus?.$on?.('regenerateElementStyles', (payload) => {
        if (!payload || !payload.type) this.regenerateElementStyles();
        this.template.elements.forEach(({ type, uid }) => {
          if (payload.type === type) {
            this.$store.dispatch('updateElementStyle', uid);
          }
        });
      });
      this.$bus?.$on?.('resetEditorPane', this.resetEditorPane);
      this.$bus?.$on?.('addTemplateFeature', (f) => this.addTemplateFeature(f));
      this.$bus?.$on?.('removeTemplateFeature', (f) => this.removeTemplateFeature(f));
      this.$bus?.$on?.('setAccountFeatures', (f) => this.setAccountFeatures(f));
      this.$bus?.$on?.('editorPagesPaneToggle', (v) => {
        this.editorPagesExpanded = v;
        if (!this.isEmbedded) {
          this.$refs.workspaceCenter.style.maxHeight = this.workspaceMaxHeight;
        }
      });
      this.$bus?.$on?.('setTeaserVisibility', this.setTeaserVisibility);

      // element resize tracking
      this.$bus?.$on?.('element-resize-heap-track', ({ eventName, element, setting, value }) => {
        track(eventName, { element, setting, value });
      });
      this.$bus?.$on?.('element-resize-lr-track', ({ eventName }) => {
        this.logRocketTrack(eventName);
      });
      this.outerCanvasObserver = new ResizeObserver(() => {
        this.checkCanvasHeight();
      });
      if (this.$refs.outerCanvas) {
        this.outerCanvasObserver.observe(this.$refs.outerCanvas);
      }

      this.wsContentObserver = new ResizeObserver(() => {
        this.checkCanvasHeight();
      });
      if (this.$refs.wsContent) {
        this.wsContentObserver.observe(this.$refs.wsContent);
      }
    },
    beforeDestroy() {
      if (channel) {
        channel.close();
      }
      if (this.outerCanvasObserver) this.outerCanvasObserver.disconnect();
      if (this.wsContentObserver) this.wsContentObserver.disconnect();
    },
    methods: {
      ...mapMutations([
        'addFields',
        'addImages',
        'addCustomField',
        'deselectAll',
        'loadTemplateStore',
        'resetEditorPane',
        'setDragInfo',
        'setMobilePreview',
        'setSelectedRow',
        'saveTemplateStore',
        'selectElementByUid',
        'setStateAttr',
        'updateData',
        'updateStyle',
        'updatePageStyle',
        'updatePageTitle',
        'updateTemplateStyle',
        'setTeaserPreview',
        'setImageSrcsOnFirstPage',
        'setFloatingActionsPosition',
        'addTemplateFeature',
        'removeTemplateFeature',
        'setAccountFeatures',
        'copyElementStyle',
        'resetCopyElementStyle',
        'setTeaserVisibility',
        'updateGlobalStyle',
      ]),
      ...mapActions([
        'loadTemplateWeb',
        'saveTemplateWeb',
        'switchInputElement',
        'removeElement',
        'addElement',
        'moveElement',
        'selectPage',
        'getImagesOnFirstPage',
        'getFonts',
        'postInitElements',
        'selectElement',
        'replaceTemplate',
        'setElementDefaults',
        'setThemeStyle',
        'regenerateElementStyles',
        'regenerateBaseStyles',
        'addPage',
        'copyPage',
        'removeRow',
        'removePage',
        'movePage',
        'updateColumnCount',
        'setSpecialElementsOnPage',
        'validateElements',
      ]),
      ...customThemeMutations(['modifyElementByUid']),
      ...mapActions('wysiwyg', ['updateWysiwyg']),
      logRocketTrack(name) {
        const logRocketInited = window.parent?._logRocketInited;
        if (logRocketInited) {
          LogRocket.track(name);
        }
      },
      setCursorIcon(action) {
        parent.document.getElementsByTagName('html')[0].classList[action]('under-copy');
        document.getElementsByTagName('html')[0].classList[action]('under-copy');
      },
      toggleRightSidebar(value) {
        window.parent.om.bus.$emit('openRightSidebar', value);
      },
      repositionQuill() {
        const TOOLBAR_HEIGHT = 110;
        let selected = document.querySelector('.om-element.selected');
        if (!selected && this.teaserPreview) {
          selected = document.querySelector('.editor-ele_mBbPvb61Q');
        }
        const toolbar = document.querySelector('.ql-toolbar');
        if (toolbar) {
          const { width: toolbarWidth } = toolbar.getClientRects()[0];
          const { top, height, left, width } = selected.getClientRects()[0];
          let leftCandidate = (width - toolbarWidth) / 2 + left;
          let topCandidate = top - TOOLBAR_HEIGHT;
          // check left
          if (leftCandidate < 10) {
            leftCandidate = 10;
          }
          // check right
          if (leftCandidate + toolbarWidth > window.innerWidth) {
            leftCandidate = window.innerWidth - toolbarWidth - 10;
          }
          // check top
          if (topCandidate < 10) {
            topCandidate = top + height + 10;
          }

          const wrapper = document.querySelector('.om-workspace-wrapper');
          if (wrapper) {
            const { y, top, height } = wrapper?.getBoundingClientRect?.() ?? {};
            const bottomPosition = y + top + height;
            const realHeight = TOOLBAR_HEIGHT * 2 + topCandidate;
            if (realHeight > bottomPosition) {
              topCandidate -= TOOLBAR_HEIGHT * 2;
            }
          }

          const finalLeft = leftCandidate > 0 ? leftCandidate : 10;
          toolbar.style.top = `${topCandidate}px`;
          toolbar.style.left = `${finalLeft}px`;
        }
      },
      outerCanvasClickHandler() {
        this.$bus?.$emit?.('selectElement', this.selectedPage?.uid);
        this.$bus?.$emit?.('outerCanvasClick');
      },
      overlayClickHandler(e) {
        if (e.target.closest('.om-page-resizer')) return;
        if (e.target.closest('.om-outer-canvas.editor-mode')) return;
        if (e.target.closest('.om-tab-wrapper-v2')) return; // teaser V2

        this.resetCopyElementStyle();

        const colorPickerActive =
          window.parent.document.querySelector('.editor-color-picker') !== null;

        if (colorPickerActive) {
          this.$bus?.$emit?.('hideColorPicker');
        }

        if (this.selectedPage.isTeaser) {
          this.$bus?.$emit?.('selectElement', this.selectedPage?.uid);
        } else {
          this.$bus?.$emit?.('deselectAll');
        }

        if (this.isPagePaneClosed()) {
          this.isRightSidebarOpen = true;
        } else {
          this.isRightSidebarOpen = false;
        }

        this.$bus?.$emit?.('overlayClick');
      },
      isPagePaneClosed() {
        const rightSideBar = window.parent.document.getElementsByClassName('page-pane-closed');
        return rightSideBar.length > 0;
      },
      eleMa(uid) {
        return uid.replace('_', '_ma_');
      },
      attachCanvasResizeSenzor(pageId) {
        const canvasElement = document.getElementById(pageId);
        if (globalID) {
          cancelAnimationFrame(globalID);
        }
        const lastSelectedElementHeight = 0;
        let lastSelectedElementTop = 0;
        let lastWidth = 0;
        let lastHeight = 0;
        const animationCallback = () => {
          // wysywig is being used
          if (this.quillToolbarInfo.show && this.selectedElement) {
            const selectedElement = document.getElementById(this.selectedElement.uid);
            // prettier-ignore
            const {
              height: selectedElementHeight,
              top: selectedElementTop,
            } = selectedElement.getBoundingClientRect();
            const selectedHeightChanged = lastSelectedElementHeight !== selectedElementHeight;
            const selectedElementTopChanged = lastSelectedElementTop !== selectedElementTop;

            if (selectedHeightChanged || selectedElementTopChanged) {
              lastSelectedElementTop = selectedElementTop;
              this.repositionQuill();
            } else {
              const height = window.innerHeight;
              const width = window.innerWidth;

              const windowDimChanged = height !== lastHeight || width !== lastWidth;
              if (windowDimChanged) {
                lastHeight = height;
                lastWidth = width;
                lastSelectedElementTop = selectedElementTop;
                this.repositionQuill();
              }
            }

            // reposition tooltip
            const overlayClasses = document.querySelector('.om-overlay').classList;
            const notPopup =
              overlayClasses.contains('nanobar') || overlayClasses.contains('om-sidebar');
            const toolbar = document.querySelector('.ql-toolbar');
            const { top: toolbarTop, height: toolbarHeight } = toolbar.getBoundingClientRect();
            const tooltip = document.querySelector('.ql-tooltip:not(.ql-hidden)');
            if (tooltip) {
              if (toolbarTop > selectedElementTop) {
                const tooltipTop = toolbarHeight - 125;
                tooltip.style.top = `${tooltipTop}px`;
              } else if (notPopup) {
                tooltip.style.top = '-25px';
              }
            }
          }
          // add CanvasLarge if size is over limit
          if (canvasElement) {
            this.isCanvasLargeSetter(canvasElement);
          }

          globalID = requestAnimationFrame(animationCallback);
        };

        requestAnimationFrame(animationCallback);
      },
      isCanvasLargeSetter(canvasElement) {
        let canvasHeight;
        if (canvasElement) {
          const boundingRect = canvasElement.getBoundingClientRect();
          canvasHeight = boundingRect.height;
        }
        if (canvasElement && canvasHeight) {
          const computedStyle = window.getComputedStyle(canvasElement, null);
          const fontSize = computedStyle
            ? parseInt(computedStyle.getPropertyValue('font-size'), 10)
            : 16;
          this.isCanvasLarge = canvasHeight / fontSize >= LARGE_CANVAS_THRESHOLD_EM;
        } else {
          this.isCanvasLarge = false;
        }
      },
      navigateTo(to) {
        this.navigateSidebar(to);
      },
      navigateSidebar(pane, level = 2) {
        _emit(this.$bus, 'activatePane', { pane, level });
      },
      showUpgradePlanModal() {
        _emit(window.parent.om.bus, 'showUpgradePlanModal');
      },
      appendCustomScripts() {
        const scriptsToAppend = [];
        const customJsByEvents = getCustomJsByEvent(this.template.customJsByEvents);
        const hasCustomJs = customJsByEvents.length !== 0;

        if (hasCustomJs) {
          customJsByEvents.forEach((e) => {
            if (e === 'pageLoad') {
              scriptsToAppend.push(this.template.customJsByEvents[e]);
            } else {
              scriptsToAppend.push(`OptiMonk.addListener(document, '${this.getEventListenerByEvent(
                e,
              )}', function (event) {
              var $ = OptiMonk.$
              var campaign = ${
                ['popupShow', 'popupFill'].includes(e)
                  ? 'OptiMonk.campaigns[parseInt(event.parameters.campaignId, 10)]'
                  : 'event.parameters.campaign'
              }

              if (campaign.campaignId === [[CAMPAIGN_ID]]) {
                ${this.template.customJsByEvents[e]}
              }
            })`);
            }
          });
        }

        if (scriptsToAppend.length) {
          appendScript(
            this.$refs.overlay,
            `window['OMCustomJS_[[CAMPAIGN_ID]]'] = function (OptiMonk, $, campaign) {${scriptsToAppend.join(
              '\n',
            )}}`,
          );
          return true;
        }

        return false;
      },
      getEventListenerByEvent(event) {
        return this.isEmbedded ? EMBEDDED_CAMPAIGN_EVENT_TYPES[event] : CAMPAIGN_EVENT_TYPES[event];
      },
      addOverflowForAnimation(element) {
        if (this.isEmbedded) {
          return;
        }
        this.$refs.workspaceCenter.style.overflow = 'hidden';
        const doIt = (e) => {
          this.$refs.workspaceCenter.style.overflow = '';
          e.target.removeEventListener('animationend', doIt);
        };
        element.addEventListener('animationend', doIt);
      },
      addOverflowForAnimations() {
        if (this.animation && this.$refs.overlayCenter) {
          this.addOverflowForAnimation(this.$refs.overlayCenter);
        }

        if (this.tabSwitchAnimation && this.$refs.tabWrapper) {
          this.addOverflowForAnimation(this.$refs.tabWrapper);
        }
      },
      replayTeaserAnimation() {
        const handler = (e) => {
          e.target.classList.remove('animated', ...tabAnimations);
          e.target.removeEventListener('animationend', handler);
        };

        if (this.editMode && this.$refs.omTabAnimation && this.tabSwitchAnimation) {
          this.$refs.omTabAnimation.addEventListener('animationend', handler);
          this.$refs.omTabAnimation.classList.add('animated', this.tabSwitchAnimation);
        }
      },
      replayTeaserAttentionSeeker() {
        const handler = (e) => {
          e.target.classList.remove('animated', ...attentionSeekers);
          e.target.removeEventListener('animationend', handler);
        };
        const type = this.hasTemplateFeature(TEMPLATE_FEATURES.NEW_TEASER)
          ? this.teaserPage.data.attentionSeeker.type
          : this.tabAttentionSeekerType;

        if (this.editMode && this.$refs.omTabAnimation && type) {
          this.$refs.omTabAnimation.addEventListener('animationend', handler);
          this.$refs.omTabAnimation.classList.add('animated', type);
        }
      },
      tabClick() {
        this.setStateAttr({ attr: 'teaserSelected', value: true });
        this.navigateTo('TeaserPane');
      },
      isRowSelected(row) {
        if (this.selectedElement) {
          return (
            row.uid === this.selectedElement.uid ||
            (this.selectedElement.type === 'OmCol' && this.selectedElement.rowId === row.uid)
          );
        }
        return false;
      },
      calculateTemplateHash() {
        const clonedState = _clone(this.$store.state);

        clonedState.template.elements.forEach((el) => {
          if (el.type !== 'OmPage') {
            this.$set(el, 'selected', false);
          }
        });

        const hash = objectHash(clonedState.template);

        if (this.$store.state.templateHash) {
          _emit(this.$bus, 'templateHashChanged', hash);
        }

        this.$store.state.templateHash = hash;
      },
      workspaceLeaving() {
        this.$bus?.$emit?.('workspaceLeaving');
      },
      calculateMaxHeight(notExpandedOffset, expandedOffset) {
        return this.mobilePreview
          ? '100vh'
          : this.editorPagesExpanded
          ? `calc(100vh - ${expandedOffset}rem)`
          : `calc(100vh - ${notExpandedOffset}rem)`;
      },
      onWsContentAnimationEnd() {
        this.checkCanvasHeight();
      },
      checkCanvasHeight() {
        const GAP = 60;
        const outerCanvas = this.$refs.outerCanvas;
        const wsContent = this.$refs.wsContent;

        if (outerCanvas && wsContent && this.mobilePreview) {
          this.shouldAlignFlexStart = outerCanvas.offsetHeight + GAP > wsContent.offsetHeight;
        }
      },
      onOuterClick() {
        this.selectElementByUid(this.selectedPage.uid);
        this.$nextTick(() => {
          this.deselectAll();
        });
      },
    },
  };
</script>

<style>
  .om-fade-transition {
    transition: all 0.2s ease;
    opacity: 1;
  }
  .om-fade-enter,
  .om-fade-leave,
  .om-fade-enter-to,
  .om-fade-leave-to {
    opacity: 0;
    transition: all 0.2s ease;
  }

  .om-fade2-transition {
    transition: all 0.3s ease;
    opacity: 1;
  }
  .om-fade2-enter,
  .om-fade2-leave,
  .om-fade2-enter-to,
  .om-fade2-leave-to {
    opacity: 0;
    transition: all 0.3s ease;
  }
  .dropzone.fillgap {
    top: 0 !important;
    bottom: 100% !important;
    height: 100% !important;
  }
</style>
<style lang="sass" scoped>
  @import '../../sass/variables/_colors.sass'

  .om-editor
    .om-editor-center
      background-color: $om-gray-500
  @media screen and (max-width: 576px)
    .om-teaser-page [data-tab-position-mobile="top"].om-tab-wrapper-v2
      top: 40px !important
</style>
<style lang="sass" src="@/editor/sass/workspace.sass" />

import { connectToDb } from './helpers/mongoHelper';
import { connectToSecondaryDb } from './helpers/SecondaryMongoHelper';
import { initQueueProcessors } from './queues';
import { app } from './app';
import { startScheduledTasks } from './helpers/scheduledTasks';

export const init = async () => {
  await connectToDb();
  await connectToSecondaryDb();

  startScheduledTasks();
  initQueueProcessors();

  return app;
};

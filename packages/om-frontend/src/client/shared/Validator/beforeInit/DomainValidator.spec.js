const { DomainValidator } = require('./DomainValidator');

describe('DomainValidator', () => {
  // Store original location
  const originalLocation = window.location;

  beforeEach(() => {
    // Delete and recreate window.location for each test
    delete window.location;
    window.location = { host: 'default.com' };
  });

  afterEach(() => {
    // Restore original location
    window.location = originalLocation;
  });

  describe('validate method', () => {
    describe('wildcard domain cases', () => {
      const wildcardTestCases = [
        {
          description: 'should return true when campaign domain is "*" (universal wildcard)',
          currentHost: 'example.com',
          campaignDomain: '*',
          expected: true,
        },
        {
          description:
            'should return true when campaign domain is "*" regardless of current domain',
          currentHost: 'any-domain.com',
          campaignDomain: '*',
          expected: true,
        },
      ];

      test.each(wildcardTestCases)('$description', ({ currentHost, campaignDomain, expected }) => {
        window.location.host = currentHost;

        const campaign = { campaignId: 'test-123', domain: campaignDomain };
        const result = DomainValidator.validate(campaign);

        expect(result).toBe(expected);
      });
    });

    describe('exact domain matching', () => {
      const exactMatchTestCases = [
        {
          description: 'should return true when campaign domain exactly matches current domain',
          currentHost: 'example.com',
          campaignDomain: 'example.com',
          expected: true,
        },
        {
          description: 'should return false when campaign domain does not match current domain',
          currentHost: 'example.com',
          campaignDomain: 'different.com',
          expected: false,
        },
        {
          description: 'should handle www prefix removal in both domains',
          currentHost: 'www.example.com',
          campaignDomain: 'example.com',
          expected: true,
        },
        {
          description: 'should handle m prefix removal in both domains',
          currentHost: 'm.example.com',
          campaignDomain: 'example.com',
          expected: true,
        },
      ];

      test.each(exactMatchTestCases)(
        '$description',
        ({ currentHost, campaignDomain, expected }) => {
          window.location.host = currentHost;

          const campaign = { campaignId: 'test-123', domain: campaignDomain };
          const result = DomainValidator.validate(campaign);

          expect(result).toBe(expected);
        },
      );
    });

    describe('subdomain wildcard matching', () => {
      const subdomainWildcardTestCases = [
        {
          description: 'should match *.example.com with sub.example.com',
          currentHost: 'sub.example.com',
          campaignDomain: '*.example.com',
          expected: true,
        },
        {
          description: 'should match *.example.com with shop.example.com',
          currentHost: 'shop.example.com',
          campaignDomain: '*.example.com',
          expected: true,
        },
        {
          description: 'should match *.example.com with www.shop.example.com (www prefix removed)',
          currentHost: 'www.shop.example.com',
          campaignDomain: '*.example.com',
          expected: true,
        },
        {
          description: 'should not match *.example.com with different.com',
          currentHost: 'different.com',
          campaignDomain: '*.example.com',
          expected: false,
        },
        {
          description: 'should not match *.example.com with example.different.com',
          currentHost: 'example.different.com',
          campaignDomain: '*.example.com',
          expected: false,
        },
      ];

      test.each(subdomainWildcardTestCases)(
        '$description',
        ({ currentHost, campaignDomain, expected }) => {
          window.location.host = currentHost;

          const campaign = { campaignId: 'test-123', domain: campaignDomain };
          const result = DomainValidator.validate(campaign);

          expect(result).toBe(expected);
        },
      );

      const additionalSubdomainTestCases = [
        {
          description: 'should match exact subdomain (not wildcard pattern)',
          currentHost: 'sub.example.com',
          campaignDomain: 'sub.example.com',
          expected: true,
        },
        {
          description: 'should not match complex subdomain patterns with *.example.com',
          currentHost: 'api.v2.example.com',
          campaignDomain: '*.example.com',
          expected: false,
          comment: 'api.v2.example.com -> v2.example.com !== example.com',
        },
        {
          description: 'should match single-level subdomains with *.example.com',
          currentHost: 'api.example.com',
          campaignDomain: '*.example.com',
          expected: true,
        },
        {
          description: 'should not match when subdomain pattern has different TLD',
          currentHost: 'sub.example.org',
          campaignDomain: '*.example.com',
          expected: false,
        },
      ];

      test.each(additionalSubdomainTestCases)(
        '$description',
        ({ currentHost, campaignDomain, expected }) => {
          window.location.host = currentHost;

          const campaign = { campaignId: 'test-123', domain: campaignDomain };
          const result = DomainValidator.validate(campaign);

          expect(result).toBe(expected);
        },
      );
    });

    describe('edge cases', () => {
      const edgeCaseTestCases = [
        {
          description: 'should handle case insensitive domain matching',
          currentHost: 'EXAMPLE.COM',
          campaignDomain: 'example.com',
          expected: true,
        },
        {
          description: 'should handle mixed case in wildcard domains',
          currentHost: 'SUB.EXAMPLE.COM',
          campaignDomain: '*.example.com',
          expected: true,
        },
        {
          description: 'should match www prefix with wildcard domains',
          currentHost: 'www.example.com',
          campaignDomain: '*.example.com',
          expected: true,
          comment: 'www.example.com becomes example.com and matches *.example.com',
        },
        {
          description: 'should match exact domain after www removal',
          currentHost: 'www.example.com',
          campaignDomain: 'example.com',
          expected: true,
          comment: 'www.example.com becomes example.com after purification',
        },
        {
          description: 'should not match wildcard pattern with different root domain',
          currentHost: 'example.com',
          campaignDomain: '*.different.com',
          expected: false,
        },
        {
          description: 'should match m prefix with wildcard domains',
          currentHost: 'm.example.com',
          campaignDomain: '*.example.com',
          expected: true,
          comment: 'm.example.com becomes example.com and matches *.example.com',
        },
        {
          description: 'should match root domain with wildcard domains',
          currentHost: 'example.com',
          campaignDomain: '*.example.com',
          expected: true,
          comment: 'example.com matches *.example.com (root domain matches wildcard)',
        },
      ];

      test.each(edgeCaseTestCases)('$description', ({ currentHost, campaignDomain, expected }) => {
        window.location.host = currentHost;

        const campaign = { campaignId: 'test-123', domain: campaignDomain };
        const result = DomainValidator.validate(campaign);

        expect(result).toBe(expected);
      });
    });
  });
});

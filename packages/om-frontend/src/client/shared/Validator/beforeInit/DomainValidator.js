import { IValidator } from '../helper/IValidator';
import { purifyDomain } from '../../Utils/domain';

const subDomainCheck = (formattedUrlPattern, formattedHostname) => {
  if (formattedUrlPattern[0] !== '*') return false;

  const wildcardBaseDomain = formattedUrlPattern.substring(formattedUrlPattern.indexOf('.') + 1);

  // If the current domain exactly matches the wildcard base domain (e.g., example.com matches *.example.com)
  if (formattedHostname === wildcardBaseDomain) {
    return true;
  }

  // Check if the current domain is a subdomain of the wildcard base domain
  // Only proceed if the current domain has a dot (to avoid matching single-word domains)
  if (formattedHostname.indexOf('.') === -1) {
    return false;
  }

  const currentDomainAfterFirstDot = formattedHostname.substring(
    formattedHostname.indexOf('.') + 1,
  );
  return wildcardBaseDomain === currentDomainAfterFirstDot;
};

export class DomainValidator extends IValidator {
  static get type() {
    return 'domain';
  }

  static validate(campaign) {
    if (!campaign.domain) {
      console.warn(`[OM] No domain for campaign: ${campaign.campaignId}`);
      return false;
    }
    const campaignDomain = purifyDomain(campaign.domain);
    const currentDomain = purifyDomain(window.location.host);
    return (
      campaignDomain === '*' ||
      campaignDomain === currentDomain ||
      subDomainCheck(campaignDomain, currentDomain)
    );
  }
}

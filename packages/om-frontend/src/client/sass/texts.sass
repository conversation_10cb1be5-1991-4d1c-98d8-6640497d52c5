+text-font-families
.ql-font-verdana
  font-family: Verdana, Geneva, sans-serif

.om-dtr-content
  color: inherit
  span,
  strong,
  u,
  s,
  em
    color: inherit
  .smart-tag
    span:not([data-ddtr-element])
      background: rgba(237, 90, 41, 0.1)
      border: none
      padding: 4px 8px
      text-align: center
      text-decoration: none
      display: inline-block
      border: 1px dashed #ED5A29
      border-radius: 4px

.om-text
  display: flex
  flex-direction: column
  width: 100%
  line-height: 1
  word-wrap: break-word
  z-index: 2
  a
    text-decoration: underline
    color: inherit
    &:hover
      text-decoration: none
  ul, ol
    list-style-type: none
    padding-left: 0

    .om-dtr-content
      position: relative
      &:has(span + br)
        font-size: inherit !important
        padding-left: 0 !important
        &:before
          display: none !important
      &:has(.ProseMirror-trailingBreak)
        font-size: var(--dyn-font-size)
        padding-left: 1.25em
        &:before
          display: inline-block
          position: absolute
          left: 0
          font-family: var(--dyn-font-family)
          font-weight: var(--dyn-font-weight)
          color: var(--dyn-color)
          line-height: 1
      span, a:has(:not(span))
        &:first-child
          padding-left: 1.25em
          &:before
            display: inline-block
            font-size: inherit
            color: inherit
            position: absolute
            left: 0
      a span
        font-size: inherit !important
      span + a span
        padding-left: 0 !important
        font-size: inherit !important
        &:before
          display: none !important
      span .smart-tag
        padding-left: 0 !important
        &-content
          padding: 4px 8px !important
          &:before
            display: none !important
        &:before
          display: none !important

ol
  counter-reset: orderedListCounter

  .om-dtr-content
    &:has(.ProseMirror-trailingBreak)
      &:before
        counter-increment: orderedListCounter
        content: counter(orderedListCounter) '.'
    span, a:has(:not(span))
      &:first-child
        &:before
          counter-increment: orderedListCounter
          content: counter(orderedListCounter) '.'

ul
  .om-dtr-content
    &:has(.ProseMirror-trailingBreak)
      &:before
        content: '●'
    span, a:has(:not(span))
      &:first-child
        &:before
          content: '●'

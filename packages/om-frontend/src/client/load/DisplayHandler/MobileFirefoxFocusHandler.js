export function calculateShift(inputParams) {
  const inputTop = inputParams.top;
  const inputOffsetTop = inputParams.offsetTop;

  let shiftWithoutScrollY = inputOffsetTop + inputTop - inputParams.height * 0.5;
  if (shiftWithoutScrollY > inputTop) {
    shiftWithoutScrollY = inputTop - inputParams.height;
  }
  return shiftWithoutScrollY / 2;
}
export const MobileFirefoxFocusHandler = {
  calculateShift,
};

/* eslint-disable no-use-before-define */
import { OptiMonk } from '../../OptiMonk';
import { Display } from './Display';
import { SafariFocusHandler } from './SafariFocusHandler';
import { MobileFirefoxFocusHandler } from './MobileFirefoxFocusHandler';
import { DIV_PREFIX } from '../../shared/Campaign/campaignConstans';
import { CAMPAIGN_TYPES } from '../ActivatedCampaignManager';
import sidebarAsPopupDisplay from './SidebarAsPopupDisplay';

export const OM_FIXED_CLASS = `${DIV_PREFIX}-holder-fixed`;
const bodyVisibleClass = 'om-body-visible';
const INPUT_FOCUSED_CLASS = 'om-input-focused';

const popupLarge = {
  boundary: 44,
  cls: 'om-large',
};
let focusInterval;
export function offsetWithScrollY(shift, lastEvent, campaign) {
  if (lastEvent === 'focus') {
    // FIXME campaign from where?
    if (typeof campaign !== 'undefined') {
      OptiMonk.CSS.style(
        campaign.getCampaignElement(),
        'top',
        `${window.scrollY - shift}px`,
        'important',
      );
    }
  }
}
export function addVisibleClass(campaign) {
  const iframeVisibleClass = Display.iframeVisibleClass;
  setTimeout(function () {
    campaign.getIFrameContainerElement().classList.add(iframeVisibleClass);
    OptiMonk.PageModifier.addOptiMonkCssClassToBody(bodyVisibleClass);
  }, 25);
}

const hasFitToScreenWithOverlayClass = (element) =>
  element.classList.contains('om-fit-to-screen-with-overlay');

export function sidebarAppearance(campaign) {
  const overlay = campaign.getOverlayElement();
  const popupOverlay = campaign.getPopupOverlay();
  const campaignElement = campaign.getCampaignElement();
  const overlayColor = popupOverlay.getAttribute('data-background-color');

  sidebarAsPopupDisplay.setSidebarCSS(campaign.getId());

  OptiMonk.CSS.setStyles(overlay, {
    display: 'block',
    background: 'transparent',
  });

  if (hasFitToScreenWithOverlayClass(popupOverlay) === false) {
    OptiMonk.CSS.setStyles(popupOverlay, {
      display: 'block',
      background: overlayColor ? 'none !important' : 'transparent',
      position: 'fixed',
    });
  }
  OptiMonk.CSS.setStyles(campaignElement, {
    width: null,
    height: null,
  });
  campaign.getOuterHolderElement().classList.remove(OM_FIXED_CLASS);
  Display.removeIosCursorFixClass();
}
export function sidebarAsPopupAppearance(campaign) {
  const overlay = campaign.getOverlayElement();
  const popupOverlay = campaign.getPopupOverlay();
  const campaignElement = campaign.getCampaignElement();
  const overlayColor = popupOverlay.getAttribute('data-background-color');
  OptiMonk.CSS.setStyles(overlay, {
    display: 'block',
    background: null,
  });
  OptiMonk.CSS.setStyles(popupOverlay, {
    display: 'block',
    background: overlayColor || null,
    position: null,
  });
  OptiMonk.CSS.setStyles(campaignElement, {
    width: null,
    height: '100vh',
  });
  campaign.getOuterHolderElement().classList.add(OM_FIXED_CLASS);
  if (!document.body.hasAttribute('data-overflow')) {
    Display.setOverflowSettings();
    Display.addIosCursorFixClass();
  }
}
export function calculateHeight(overlay, canvas) {
  const fontSize = overlay
    ? OptiMonk.parseInt(getComputedStyle(overlay).getPropertyValue('font-size'))
    : 16;
  const height = canvas
    ? OptiMonk.parseInt(getComputedStyle(canvas).getPropertyValue('height'))
    : 0;
  return height / fontSize;
}

export function popupHeightListener(campaign) {
  const outerCanvas = campaign.getOuterCanvasElement();
  const overlayCenter = outerCanvas.parentElement;
  if (window.innerWidth > 576) {
    overlayCenter.style.alignItems = null;
    return;
  }
  const outerCanvasHeight = outerCanvas.offsetHeight;
  const overlayCenterHeight = overlayCenter.offsetHeight;
  if (overlayCenterHeight <= outerCanvasHeight) {
    overlayCenter.style.alignItems = 'flex-start';
  } else if (outerCanvas) {
    overlayCenter.style.alignItems = null;
  }
}
export function onResizeSidebar(campaign) {
  if (campaign.closed || campaign.minimized) return;

  if (
    (OptiMonk.platform.isIpad || OptiMonk.platform.isIphone || OptiMonk.platform.isIpod) &&
    campaign.activeInput
  ) {
    campaign.activeInput.blur();
  }

  sidebarAsPopupDisplay.updateDisplayMode(campaign.getId());

  const hasActivePopup = OptiMonk.ActivatedCampaignManager.hasActiveType(CAMPAIGN_TYPES.POPUP);
  const sidebarAsPopup = sidebarAsPopupDisplay.isPopup(campaign.getId());

  if (!sidebarAsPopup) {
    sidebarAppearance(campaign);
  } else if (sidebarAsPopup && !hasActivePopup) {
    sidebarAsPopupAppearance(campaign);
  }
}

export function onAfterAppendBody(campaign) {
  const htmlEl = document.querySelector('html');
  const campaignWrapper = campaign.getCampaignElement();
  const omCanvas = campaignWrapper.querySelector('.om-overlay .om-canvas');

  if (campaign.isPopup()) {
    OptiMonk.addListener(window, 'resize', function () {
      popupHeightListener(campaign);
    });
    OptiMonk.addListener(htmlEl, 'optimonk#campaign-popup-show', () => {
      popupHeightListener(campaign);
    });
    OptiMonk.addListener(omCanvas, 'animationstart', () => {
      popupHeightListener(campaign);
    });
  } else if (campaign.isSidebar()) {
    sidebarAsPopupDisplay.updateDisplayMode(campaign.getId());
    OptiMonk.addListener(window, 'resize', function () {
      onResizeSidebar(campaign);
      popupHeightListener(campaign);
    });
    OptiMonk.addListener(htmlEl, 'optimonk#campaign-popup-show', () => {
      popupHeightListener(campaign);
    });

    OptiMonk.addListener(omCanvas, 'animationstart', () => {
      popupHeightListener(campaign);
    });
  } else if (campaign.isNanobar()) {
    OptiMonk.addListener(window, 'resize', function () {
      const hasActiveNanobar = OptiMonk.ActivatedCampaignManager.hasActiveType(
        CAMPAIGN_TYPES.NANOBAR,
      );

      if (hasActiveNanobar) {
        setPageOffset(campaign.getPopupOverlay());
      }
    });
  }
  if (OptiMonkRegistry.isMobile === 0) {
    return;
  }
  focusInterval = undefined;
  let lastEvent = '';

  OptiMonk.addListener(htmlEl, 'optimonk#campaign-popup-input-focus', function (event) {
    if (OptiMonk.parseInt(campaign.getId()) !== OptiMonk.parseInt(event.parameters.campaignId)) {
      return;
    }

    if (!OptiMonkRegistry.keyboardDetection.isKeyboardOpen()) {
      return;
    }

    // Class for custom css when popup .om-overlay-center wrongly positioned on input focus
    const overlay = campaign.getPopupOverlay();
    overlay.classList.add(INPUT_FOCUSED_CLASS);

    removePopupOverlayHeight(campaign.getId());

    if (OptiMonkRegistry.isMobile && OptiMonk.browser.isFirefox) {
      return repositionPopup(campaign, lastEvent, event);
    }

    if (
      campaign.isSidebar() &&
      (OptiMonk.platform.isIpad || OptiMonk.platform.isIphone || OptiMonk.platform.isIpod)
    ) {
      repositionSidebar(campaign, lastEvent, event);
    } else if (campaign.isPopup()) {
      repositionPopup(campaign, lastEvent, event);
    }
  });

  OptiMonk.addListener(htmlEl, 'optimonk#campaign-popup-input-blur', (event) => {
    const outerCanvasElement = campaign.getOuterCanvasElement();
    lastEvent = 'blur';
    clearInterval(focusInterval);
    OptiMonk.removeListener(document, 'scroll', offsetWithScrollY);
    OptiMonk.CSS.style(outerCanvasElement, 'top', null);

    const overlay = campaign.getPopupOverlay();
    overlay.classList.remove(INPUT_FOCUSED_CLASS);

    setTimeout(() => {
      setPopupOverlayHeightViewport(campaign.getId());
    }, 120);

    if (OptiMonkRegistry.isMobile && OptiMonk.browser.isFirefox) {
      OptiMonk.CSS.style(outerCanvasElement, 'position', null);
      event.parameters.targetElement.classList.remove('om-input-focus');
    }

    campaign.getPopupOverlay().classList.remove('undefined');
    if (campaign.isSidebar()) onResizeSidebar(campaign);
  });
  // Exit intent sidebar fix
  OptiMonk.addListener(htmlEl, 'optimonk#campaign-close', function () {
    const hasActiveSidebar = OptiMonk.ActivatedCampaignManager.hasActiveType(
      CAMPAIGN_TYPES.SIDEBAR,
    );

    if (hasActiveSidebar) {
      OptiMonk.triggerEvent(window, 'resize');
    }
  });
}
export function minimize(campaign, sizeParams) {
  const poweredByElement = campaign.getPoweredByOptiMonkElement();
  const holder = campaign.getOuterHolderElement();
  const popupOverlay = campaign.getPopupOverlay();
  if (popupOverlay.style.display !== 'none') {
    if (
      campaign.isPopup() ||
      (campaign.isSidebar() && sidebarAsPopupDisplay.isPopup(campaign.getId()))
    ) {
      Display.restoreOverflowSettings();
    }
    Display.removeIosCursorFixClass();
    Display.playClosingAnimation(campaign, () => {
      if (campaign.minimized) {
        holder.classList.remove(OM_FIXED_CLASS);
        OptiMonk.CSS.style(popupOverlay, 'display', 'none');
        import('../Common/Teaser').then(({ Teaser }) => {
          const campaignHasAfterValue = campaign.getAfterValue();
          setTimeout(function () {
            if (!campaign.minimized) return;
            Teaser.show(campaign, sizeParams);
          }, campaignHasAfterValue);
        });
      }
    });
  } else {
    holder.classList.remove(OM_FIXED_CLASS);
    import('../Common/Teaser').then(({ Teaser }) => {
      Teaser.show(campaign, sizeParams);
    });
  }
  if (poweredByElement) {
    OptiMonk.CSS.style(poweredByElement, 'display', 'none');
  }
  if (campaign.isNanobar()) {
    OptiMonk.PageModifier.restoreBody();
  }
  OptiMonk.triggerEvent(campaign.getCampaignElement(), 'optimonk#mimize-popup');
}
export async function restoreMinimized(campaign) {
  const campaignElement = campaign.getCampaignElement();
  const overlayElement = campaign.getOverlayElement();
  const poweredByElement = campaign.getPoweredByOptiMonkElement();
  const popupOverlay = campaign.getPopupOverlay();
  const animatedOverlay = campaign.getPopupOverlay().querySelector('.om-overlay-center');
  const holderElement = campaign.getOuterHolderElement();
  const effect = campaign.getEffect();
  if (effect) {
    animatedOverlay.classList.add('om-animated', `om-${effect}`);
  }
  OptiMonk.CSS.style(popupOverlay, 'display', 'block');

  const { Teaser } = await import('../Common/Teaser');
  Teaser.hide(campaign);

  OptiMonk.CSS.setStyles(overlayElement, {
    width: null,
    height: null,
    left: null,
    right: null,
    top: null,
    bottom: null,
    'pointer-events': 'all',
  });

  if (campaign.isSidebar() && !sidebarAsPopupDisplay.isPopup(campaign.getId())) {
    displaySidebar(campaign, true);
    OptiMonk.CSS.setStyles(holderElement, {
      width: '100%',
      height: '100%',
      'pointer-events': 'none',
    });
    return;
  }

  if (campaign.isNanobar()) {
    displayNanobar(campaign, true);
    return;
  }

  if (campaign.isSidebar() && sidebarAsPopupDisplay.isPopup(campaign.getId())) {
    OptiMonk.CSS.setStyles(campaign.getPopupOverlay(), {
      background: null,
      position: null,
    });
  }

  holderElement.classList.add(OM_FIXED_CLASS);
  OptiMonk.CSS.setStyles(holderElement, {
    left: null,
    top: null,
  });
  if (
    campaign.isPopup() ||
    (campaign.isSidebar() && sidebarAsPopupDisplay.isPopup(campaign.getId()))
  ) {
    Display.setOverflowSettings();
  }
  Display.addIosCursorFixClass();
  if (poweredByElement) {
    OptiMonk.CSS.style(poweredByElement, 'display', null);
  }
  OptiMonk.CSS.setStyles(campaignElement, {
    height: '100vh',
  });
}
export function displayCampaign(campaignId) {
  // @ts-ignore
  if (window.OMReloading) return;
  const campaign = OptiMonk.campaigns[campaignId];
  const overlayElement = campaign.getOverlayElement();
  const holder = campaign.getOuterHolderElement();
  addVisibleClass(campaign);
  if (campaign.isSidebar()) {
    displaySidebar(campaign);
    return;
  }
  if (campaign.isNanobar()) {
    displayNanobar(campaign);
    return;
  }
  OptiMonk.CSS.style(campaign.getCampaignElement(), 'height', '100vh');
  overlayElement.style.display = 'block';
  holder.classList.add(OM_FIXED_CLASS);
}

function _removeClassOnAnimationEnd(campaign, overlay) {
  overlay.querySelector('.om-overlay-center').addEventListener('animationend', () => {
    campaign.getHolderElement()?.classList?.remove('opening');
  });
}

export function displayPopup(campaignId) {
  // @ts-ignore
  if (window.OMReloading) return;
  const campaign = OptiMonk.campaigns[campaignId];
  campaign.getHolderElement()?.classList?.add('opening');
  const overlayElement = campaign.getOverlayElement();
  const poweredByElement = campaign.getPoweredByOptiMonkElement();
  const popupOverlay = campaign.getPopupOverlay();
  _removeClassOnAnimationEnd(campaign, popupOverlay);
  OptiMonk.CSS.style(popupOverlay, 'display', 'block');
  overlayElement.style.display = 'block';

  if (campaign.minimized) {
    import('../Common/Teaser').then(({ Teaser }) => {
      Teaser.hide(campaign);
      campaign.getCookie().setTeaserClosed();
    });
  } else if (
    campaign.isPopup() ||
    (campaign.isSidebar() && sidebarAsPopupDisplay.isPopup(campaign.getId()))
  ) {
    Display.setOverflowSettings();
  }
  OptiMonk.CSS.setStyles(overlayElement, {
    width: null,
    height: null,
    left: null,
    right: null,
    top: null,
    bottom: null,
    'pointer-events': 'all',
  });
  if (campaign.isSidebar() && !sidebarAsPopupDisplay.isPopup(campaign.getId())) {
    displaySidebar(campaign, true);
    return;
  }
  if (campaign.isPopup()) {
    if (OptiMonkRegistry.isMobile && !campaign.minimized) {
      campaign.addViewportSizeListener();
    }
  }
  if (campaign.isNanobar()) {
    displayNanobar(campaign, true);
    return;
  }
  if (campaign.isSidebar() && sidebarAsPopupDisplay.isPopup(campaign.getId())) {
    OptiMonk.CSS.setStyles(campaign.getPopupOverlay(), {
      background: null,
      position: null,
    });
  }

  const holder = campaign.getOuterHolderElement();
  holder.classList.add(OM_FIXED_CLASS);
  Display.addIosCursorFixClass();
  if (poweredByElement) {
    OptiMonk.CSS.style(poweredByElement, 'display', null);
  }

  const iFrameElement = campaign.getIFrameElement();
  OptiMonk.CSS.setStyles(iFrameElement, { height: '100vh' });
}
export function removeFixedStyles(campaign) {
  campaign.getOuterHolderElement().classList.remove(OM_FIXED_CLASS);
  Display.removeIosCursorFixClass();
}
export function displaySidebar(campaign, restored = false) {
  const hasActivePopup = OptiMonk.ActivatedCampaignManager.hasActiveType(CAMPAIGN_TYPES.POPUP);

  if (!hasActivePopup) {
    removeFixedStyles(campaign);
  }
  const isTabbedBefore = campaign.isTabbedBeforePopup();
  OptiMonk.CSS.setStyles(campaign.getOverlayElement(), {
    display: 'block',
    background: 'transparent',
  });

  if (!sidebarAsPopupDisplay.isPopup(campaign.getId())) {
    const popupOverlay = campaign.getPopupOverlay();
    OptiMonk.CSS.setStyles(popupOverlay, {
      display: isTabbedBefore && !restored ? 'none' : 'block',
      position: 'fixed',
    });
    OptiMonk.CSS.setStyles(campaign.getCampaignElement(), {
      width: null,
      height: null,
    });
  }
  onResizeSidebar(campaign);
}
export function displayNanobar(campaign, restored = false) {
  const popupOverlay = campaign.getPopupOverlay();
  const isTabbedBefore = campaign.isTabbedBeforePopup();
  OptiMonk.CSS.setStyles(popupOverlay, {
    position: 'fixed',
    width: '100vw',
    display: isTabbedBefore && !restored ? 'none' : 'block',
  });
  OptiMonk.CSS.setStyles(campaign.getOverlayElement(), {
    display: 'block',
    background: 'transparent',
  });
  OptiMonk.PageModifier.saveBody();
  setPageOffset(popupOverlay);
}
function setBodyBcgPosAndPadding(height) {
  const backgroundPosition = OptiMonk.getStyle(document.body, 'background-position');
  const backgroundPositionArray = backgroundPosition.split(' ');
  backgroundPositionArray.pop();
  backgroundPositionArray.push(height);
  document.body.style.backgroundPosition = backgroundPositionArray.join(' ');
  document.body.style.paddingTop = height;
}
export function setPageOffset(overlay) {
  const height = getComputedStyle(overlay).height;
  const isMobile = OptiMonkRegistry.isMobile;

  if (isMobile) {
    if (overlay.classList.contains('nano-mobile-pos-bottom')) {
      document.body.style.paddingBottom = height;
      return;
    }

    if (overlay.classList.contains('nano-mobile-pos-top')) {
      setBodyBcgPosAndPadding(height);
      return;
    }
  }

  if (overlay.classList.contains('top')) {
    setBodyBcgPosAndPadding(height);
  } else {
    document.body.style.paddingBottom = height;
  }
}
export function repositionSidebar(campaign, lastEvent, event) {
  if (campaign.minimized || campaign.closed) {
    return;
  }
  campaign.getPopupOverlay().classList.add('undefined');
  sidebarAsPopupAppearance(campaign);
  setTimeout(() => {
    repositionPopup(campaign, lastEvent, event);
  }, 350);
}

export function repositionPopup(campaign, lastEvent, event) {
  if (campaign.minimized || campaign.closed) {
    return;
  }
  const outerCanvasElement = campaign.getOuterCanvasElement();
  focusInterval = window.setTimeout(function mobileInputFocusHandler() {
    const inputTop = event.parameters.top;
    let shift;
    if (
      OptiMonk.browser.isFirefox &&
      !event.parameters.targetElement.classList.contains('om-input-focus')
    ) {
      shift = MobileFirefoxFocusHandler.calculateShift(event.parameters);
      event.parameters.targetElement.classList.add('om-input-focus');
      OptiMonk.CSS.style(outerCanvasElement, 'position', `absolute`);
      OptiMonk.CSS.style(outerCanvasElement, 'top', `${shift - inputTop}px`, 'important');
      return;
    }

    if (OptiMonk.platform.isIphone) {
      shift = SafariFocusHandler.calculateShift(event.parameters);
      OptiMonk.CSS.style(outerCanvasElement, 'top', `${shift - inputTop}px`, 'important');
    } else if (OptiMonk.browser.isSafari) {
      shift = SafariFocusHandler.calculateShift(event.parameters);
      OptiMonk.CSS.style(outerCanvasElement, 'top', `${window.scrollY - shift}px`, 'important');
      OptiMonk.addListener(document, 'scroll', function () {
        offsetWithScrollY(shift, lastEvent);
      });
    }
  }, 10);
}

export function setPopupOverlayHeightViewport(campaignId) {
  const campaign = OptiMonk.campaigns[campaignId];
  const holderElement = campaign.getHolderElement();
  const isDesiredToCenter = holderElement.querySelector('.om-overlay-mobile-center');
  const isDesiredToBottom = holderElement.querySelector('.om-overlay-mobile-bottom');
  const isLuckyWheel = holderElement.querySelector('.om-lucky-wheel');

  if (OptiMonkRegistry.isMobile) {
    if (
      (isDesiredToCenter || isDesiredToBottom) &&
      !isLuckyWheel &&
      !campaign.isNanobar() &&
      !campaign.getFullscreenClass()
    ) {
      const element = campaign.getPopupOverlay().querySelector('.om-overlay-center');
      element.style['align-items'] = null;
      element.style.height = '100dvh';
    }

    if (campaign.getFullscreenClass()) {
      campaign.setFullscreenOverlayHeight();
    }
  }
}

export function removePopupOverlayHeight(campaignId) {
  const campaign = OptiMonk.campaigns[campaignId];
  const element = campaign.getPopupOverlay().querySelector('.om-overlay-center');
  element.style.height = null;
  element.style['align-items'] = 'flex-end';
}

export function closeCampaignPopup(campaign) {
  if (campaign.getFullscreenClass() && OptiMonkRegistry.isMobile) {
    const element = campaign.getPopupOverlay().querySelector('.om-overlay-center');
    element.style.height = null;
  }

  campaign.removeViewportSizeListener();

  return Display.closeCampaignPopup(campaign);
}

export function removeVisibleClass() {}
export function setOverflowSettings() {}
export function saveOverflowSettings() {}
export function restoreOverflowSettings() {}
export function removeOverflowDataAttributes() {}
export function addIosCursorFixClass() {}
export function removeIosCursorFixClass() {}
export function centerPopup() {}
export function resizeCampaignIFrame() {}
export function isSidebarAsPopup(campaignId) {
  return sidebarAsPopupDisplay.isPopup(campaignId);
}
export const InlineCampaignDisplay = {
  offsetWithScrollY,
  addVisibleClass,
  sidebarAppearance,
  sidebarAsPopupAppearance,
  calculateHeight,
  onResizeSidebar,
  onAfterAppendBody,
  minimize,
  restoreMinimized,
  displayCampaign,
  displayPopup,
  removeFixedStyles,
  displaySidebar,
  displayNanobar,
  setPageOffset,
  repositionSidebar,
  repositionPopup,
  setPopupOverlayHeightViewport,
  closeCampaignPopup,
  removeVisibleClass,
  setOverflowSettings,
  saveOverflowSettings,
  restoreOverflowSettings,
  removeOverflowDataAttributes,
  addIosCursorFixClass,
  removeIosCursorFixClass,
  centerPopup,
  resizeCampaignIFrame,
  isSidebarAsPopup,
};

import { AllowBlockEmailValidator } from './Validators/AllowBlockEmailValidator';
import { AllowOnlyNewLeadEmailValidator } from './Validators/AllowOnlyNewLeadEmailValidator';
import { BaseEmailValidator } from './Validators/BaseEmailValidator';
import { BaseValidator } from './Validators/BaseValidator';
import {
  fixTrue,
  notEmpty,
  url,
  optionalUrl,
  checkbox,
  phoneNumber,
} from './Validators/validatorFunctions';
import { OptionalEmailValidator } from './Validators/OptionalEmailValidator';
import { AttributeBasedValidator } from './Validators/AttributeBasedValidator';
import { PhoneNumberValidator } from './Validators/PhoneNumberValidator';
import { OptionalPhoneNumberValidator } from './Validators/OptionalPhoneNumberValidator';
import { SpellCheckValidator } from './Validators/SpellCheckValidator';

const PICKER_GROUP_TYPES = ['checkbox', 'radio'];

export class InputValidatorFactory {
  static createValidator(
    validatorType,
    allValidatorTypesOfInput,
    errorMessages,
    inputAttributes,
    requestService,
  ) {
    const inputType = inputAttributes.getInputType();

    if (PICKER_GROUP_TYPES.includes(inputType)) {
      return InputValidatorFactory.createPickedGroupInputValidator(
        validatorType,
        errorMessages,
        inputAttributes,
      );
    }

    return InputValidatorFactory.createNormalInputValidator(
      validatorType,
      allValidatorTypesOfInput,
      errorMessages,
      inputAttributes,
      requestService,
    );
  }

  static createNormalInputValidator(
    validatorType,
    allValidatorTypesOfInput,
    errorMessages,
    inputAttributes,
    requestService,
  ) {
    const inputValue = inputAttributes.getValue();
    switch (validatorType) {
      case 'required':
      case 'empty':
        return new BaseValidator(inputValue, errorMessages, notEmpty);
      case 'url':
        return new BaseValidator(inputValue, errorMessages, url);
      case 'optionalUrl':
        return new BaseValidator(inputValue, errorMessages, optionalUrl);
      case 'email':
        return new BaseEmailValidator(inputValue, errorMessages, requestService);
      case 'allowBlock':
        return new AllowBlockEmailValidator(
          inputAttributes,
          allValidatorTypesOfInput,
          errorMessages,
        );
      case 'allowOnlyNewLead':
        return new AllowOnlyNewLeadEmailValidator(inputValue, errorMessages, requestService);
      case 'optionalEmail':
        return new OptionalEmailValidator(inputValue, errorMessages);
      case 'phoneNumber':
        return new PhoneNumberValidator(inputAttributes, errorMessages, phoneNumber);
      case 'optionalPhoneNumber':
        return new OptionalPhoneNumberValidator(inputAttributes, errorMessages, phoneNumber);
      case 'spellCheck':
        return new SpellCheckValidator(inputAttributes);
      default:
        return new BaseValidator(inputValue, errorMessages, fixTrue);
    }
  }

  static createPickedGroupInputValidator(validatorType, errorMessages, inputAttributes) {
    switch (validatorType) {
      case 'required':
        return new AttributeBasedValidator(errorMessages, checkbox, inputAttributes);
      default:
        return new BaseValidator(null, errorMessages, fixTrue);
    }
  }
}

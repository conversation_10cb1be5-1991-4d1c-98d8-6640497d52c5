/* eslint-disable camelcase */
require('dotenv-flow').config();

const request = require('supertest');
const express = require('express');
const moment = require('moment');
const { COUPON_TYPES } = require('@om/payment/src/helpers/couponEnums');
const {
  BANK_TRANSFER_RECURRING_AND_EXPIRATION_DATE_DIFF_IN_DAYS,
} = require('@om/payment/src/constants');
const { connect } = require('../../../util/mongo');
const apiController = require('../../../api');
const dbHelper = require('../../helpers/dbTestHelper');
const mysqlTestHelper = require('../../helpers/mysqlTestHelper');
const mongoTestHelper = require('../../helpers/mongoTestHelper');
const orderRepository = require('../../../api/lib/repositories/orderRepository');
const couponRepository = require('../../../api/lib/repositories/couponRepository');

const {
  ActivatedCouponService,
} = require('../../../api/lib/services/coupon/activatedCouponService');
const {
  CouponValidatorService,
} = require('../../../api/lib/services/coupon/couponValidatorService');

const app = express();
const invoice = require('../../../api/lib/services/invoiceService');
const om = require('../../../api/integrations/optimonk');

const couponFixtures = require('../../fixtures/Coupon');
const { getExpiredUser, getExpiredUserWithFlexiPay } = require('../../fixtures/BankTransferUser');
const {
  assert_orderWithSuccesfulFlexiPay,
  assert_orderWithDeclinedFlexiPay,
  assert_orderWithDiscountedPrice,
} = require('../planChange/assertHelperBankTransfer');

const initSpiesAndMocks = () => {
  jest.spyOn(orderRepository, 'getLastOrderForCustomer').mockImplementation(() => {
    return { order_status_id: 5 };
  });
  const generateProformaSpy = jest.spyOn(invoice, 'generateProforma').mockImplementation(() => {
    return {
      success: true,
      data: {
        response: {
          pdfString: '',
          invoiceId: 1,
        },
      },
    };
  });
  const sendMailSpy = jest.spyOn(om.prototype, 'sendProformaEmail').mockImplementation(() => {
    return { success: true };
  });

  return { generateProformaSpy, sendMailSpy };
};

const mockActivatedCoupon = (coupon, activatedCoupon) => {
  jest.spyOn(ActivatedCouponService.prototype, '_getActiveCoupon').mockImplementation(() => {
    return activatedCoupon;
  });
  jest.spyOn(CouponValidatorService.prototype, 'validateCouponForOrder').mockImplementation(() => {
    return true;
  });
  jest.spyOn(couponRepository, 'get').mockImplementation(() => {
    return coupon;
  });
  const unsetActivatedSpy = jest
    .spyOn(ActivatedCouponService.prototype, '_updateActives')
    .mockImplementation(() => {});

  return { unsetActivatedSpy };
};

beforeAll(async () => {
  app.use('/api', apiController);
  await connect(app);
});

beforeEach(async () => {
  await Promise.all([mysqlTestHelper.clearData(), mongoTestHelper.clearData()]);
  await Promise.all([mysqlTestHelper.insertBaseFixtures()]);
  jest.clearAllMocks();
  jest.resetAllMocks();
});

describe('Bank-transfer recurring', () => {
  test('Recurring with succesful FlexiPay charge', async () => {
    const toPackage = 'Package-HU-SILVER-1';
    const user = getExpiredUserWithFlexiPay(toPackage, 500, { maximumTotal: 99999 });
    await dbHelper.createUser(user);

    const { generateProformaSpy, sendMailSpy } = initSpiesAndMocks();

    await request(app)
      .get('/api/payment/recurring/bank-transfer')
      .query({ cron_token: process.env.CRON_TOKEN })
      .expect(200);

    expect(generateProformaSpy).toHaveBeenCalledTimes(1);
    expect(sendMailSpy).toHaveBeenCalledTimes(1);

    assert_orderWithSuccesfulFlexiPay({ user, toPackage });
  });

  test('Recurring with unsuccesful FlexiPay charge (reached maximum total)', async () => {
    const toPackage = 'Package-HU-SILVER-1';
    const user = getExpiredUserWithFlexiPay(toPackage, 500, { maximumTotal: 1 });
    await dbHelper.createUser(user);

    const { generateProformaSpy, sendMailSpy } = initSpiesAndMocks();

    await request(app)
      .get('/api/payment/recurring/bank-transfer')
      .query({ cron_token: process.env.CRON_TOKEN })
      .expect(200);

    expect(generateProformaSpy).toHaveBeenCalledTimes(1);
    expect(sendMailSpy).toHaveBeenCalledTimes(1);

    assert_orderWithDeclinedFlexiPay({ user, toPackage });
  });

  test('Recurring with next-billing coupon activation', async () => {
    const toPackage = 'Package-HU-SILVER-1';
    const nextBillingDate = moment
      .utc()
      .startOf('day')
      .add(BANK_TRANSFER_RECURRING_AND_EXPIRATION_DATE_DIFF_IN_DAYS - 14, 'days');

    const coupon = couponFixtures.getBaseCoupon(COUPON_TYPES.RECURRING_PERCENT);
    const activatedCoupon = couponFixtures.getNextBillingActivatedCoupon(
      COUPON_TYPES.RECURRING_PERCENT,
      nextBillingDate,
    );

    const user = getExpiredUser(toPackage);
    user.account.billing.datePaid = nextBillingDate.toDate();
    user.account.billing.dateExpires = nextBillingDate.clone().add(14, 'days').toDate();
    await dbHelper.createUser(user);

    const { generateProformaSpy, sendMailSpy } = initSpiesAndMocks();
    const { unsetActivatedSpy } = mockActivatedCoupon(coupon, activatedCoupon);

    await request(app)
      .get('/api/payment/recurring/bank-transfer')
      .query({ cron_token: process.env.CRON_TOKEN })
      .expect(200);

    expect(unsetActivatedSpy).toHaveBeenCalledTimes(0);
    expect(generateProformaSpy).toHaveBeenCalledTimes(1);
    expect(sendMailSpy).toHaveBeenCalledTimes(1);

    assert_orderWithDiscountedPrice({ user, toPackage, coupon, fixedPrice: null });
  });
});

afterAll(async () => {
  await Promise.all([mysqlTestHelper.closeConnection(), mongoTestHelper.closeConnection()]);
});

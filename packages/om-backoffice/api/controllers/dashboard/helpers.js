const moment = require('moment');
const salesMysql = require('../helpers/salesMysql');
const {
  getAccountCampaigStatistics,
  getAccountStatistics,
  getAccountsWithRegion,
  getActiveAccounts,
  getAllActiveAccountsBetween,
  getLeadCountByRegion,
  getSubAccountDatabaseIds,
  getShopPlatformAccountIds,
} = require('./queries');
const { getCollectionRef } = require('../../../util/mongo');

const REGION_PACKAGE_LANG_CODES = {
  germany: 'DE',
  hungary: 'HU',
  usa: 'EN',
};

const formatOldPackageNames = (packageName) => {
  let result;
  if (packageName.startsWith('FREE')) {
    result = 'free';
  } else if (packageName.startsWith('TRIAL')) {
    result = 'trial';
  } else if (packageName.startsWith('AGENCY')) {
    result = 'agency';
  } else if (packageName.startsWith('BASIC')) {
    result = 'basic';
  } else if (packageName.startsWith('BRONZE')) {
    result = 'bronze';
  } else if (packageName.startsWith('BUSINESS')) {
    result = 'business';
  } else if (packageName.startsWith('DIAMOND')) {
    result = 'diamond';
  } else if (packageName.startsWith('ENTERPRISE')) {
    result = 'enterprise';
  } else if (packageName.startsWith('GOLD')) {
    result = 'gold';
  } else if (packageName.startsWith('PLATINUM')) {
    result = 'platinum';
  } else if (packageName.startsWith('PROFESSIONAL')) {
    result = 'professional';
  } else if (packageName.startsWith('SILVER')) {
    result = 'silver';
  } else if (packageName.startsWith('ESSENTIAL')) {
    result = 'essential';
  } else if (packageName.startsWith('GROWTH')) {
    result = 'growth';
  } else if (packageName.startsWith('PREMIUM')) {
    result = 'premium';
  } else if (packageName.startsWith('MASTER')) {
    result = 'master';
  }
  return result;
};

const packages = () => {
  return {
    basic: 0,
    business: 0,
    professional: 0,
    bronze: 0,
    silver: 0,
    gold: 0,
    platinum: 0,
    agency: 0,
    enterprise: 0,
    diamond: 0,
    essential: 0,
    growth: 0,
    premium: 0,
    master: 0,
  };
};

const periods = () => {
  return {
    monthly: 0,
    quarterly: 0,
    yearly: 0,
  };
};

const packagesToMap = async () => {
  const products = await salesMysql.getProducts();
  const PACKAGE_PREFIX = 'Package-';
  const map = {};
  products.forEach((e) => {
    // add both db and uppercase versions
    if (e.name.startsWith(PACKAGE_PREFIX)) {
      const upperPackage = e.name.substr(PACKAGE_PREFIX.length).toUpperCase();
      const key = PACKAGE_PREFIX + upperPackage;
      map[e.name] = e.price;
      map[key] = e.price;
    }
  });
  return map;
};

const region = () => {
  return {
    count: 0,
    packages: packages(),
    periods: periods(),
  };
};

const formatPeriod = (period) => {
  let result;
  switch (period) {
    case 1:
      result = 'monthly';
      break;
    case 3:
      result = 'quarterly';
      break;
    case 12:
      result = 'yearly';
      break;
  }
  return result;
};

const getCurrencies = () => {
  const usd = 250;
  const eur = 320;
  return { usd, eur };
};

const splitIdsByRegion = (accountIds, accountMap) => {
  const result = { com: 0, hu: 0, all: 0, accountIds };
  for (const accountId of accountIds) {
    const foundAccount = accountMap[accountId];
    if (!foundAccount) {
      // console.log('####not found account with id =', accountId)
      continue;
    }
    result.all++;
    const isCom = foundAccount.sku.indexOf('-HU-') === -1;
    if (isCom) {
      result.com++;
    } else {
      result.hu++;
    }
  }
  return result;
};

const getPackageInfo = (account, packagesMap) => {
  const langCode = REGION_PACKAGE_LANG_CODES[account.region.toLowerCase()];
  const sku = `Package-${langCode}-${account.package}-${account.period}`;
  const price = packagesMap[sku];

  return { omId: account.databaseId, sku, price };
};

const monthDiffDiscounts = {
  0: 0,
  3: 0.9,
  4: 0.8,
  6: 0.6,
  12: 0.6,
};

const getDiscountModifier = (account) => {
  const paid = moment(account.datePaid);
  const expires = moment(account.dateExpires);
  let monthDiff;
  const _FREE_ACCOUNTS = [187, 3595, 13661];
  const _6_PLUS_6_IDS = [18939, 23115, 34153, 51160];
  const _12_PLUS_12_IDS = [359, 4372, 19763, 20514, 23401];
  if (_FREE_ACCOUNTS.includes(account.databaseId)) {
    monthDiff = 0;
  } else if (_6_PLUS_6_IDS.includes(account.databaseId)) {
    monthDiff = 6;
  } else if (_12_PLUS_12_IDS.includes(account.databaseId)) {
    monthDiff = 12;
  } else {
    monthDiff = Math.floor(moment.duration(expires.diff(paid)).asMonths());
  }

  let modifier = 1;
  if (monthDiff >= 3) {
    const discount = monthDiffDiscounts[monthDiff];
    if (discount) {
      modifier = monthDiffDiscounts[monthDiff];
    } else {
      // console.log('@monthDiff', monthDiff, account.email, account.databaseId)
    }
  }
  // blueknow
  if (account.databaseId === 50005) {
    modifier = 0.7;
  }

  return modifier;
};

const isMidMarketByPrice = (period, price) => {
  return (period === 1 && price >= 49500) || (period === 12 && price >= 495000);
};

const getAccountMRRs = (foundPackage, currencies, date = new Date()) => {
  return foundPackage;
};

const getAccountMrr = (account, packagesMap, currencies) => {
  const langCode = REGION_PACKAGE_LANG_CODES[account.region.toLowerCase()];
  const auroraPackageName = `Package-${langCode}-${account.package}-${account.period}`;
  let price = packagesMap[auroraPackageName];

  const discountModifier = getDiscountModifier(account);
  price *= discountModifier;

  const convAmount = langCode === 'EN' || langCode === 'DE' ? currencies.usd : 1;
  const platformModifier = 1; // deprecated since shopify revenue share change
  const accountMrr = (price * platformModifier) / convAmount / account.period;
  const isMidMarket = isMidMarketByPrice(account.period, price);
  const mrr = { com: 0, hu: 0, isMidMarket };
  if (langCode === 'EN' || langCode === 'DE') {
    mrr.com += accountMrr;
  } else {
    mrr.hu += accountMrr;
  }
  return mrr;
};

const seperateActiveAccounts = (accounts, activeAccounts, expiredAccounts) => {
  const today = new Date(moment().format('YYYY-MM-DD'));
  accounts.forEach((e) => {
    if (e.dateExpires.getTime() >= today.getTime()) {
      activeAccounts.push(e);
    } else {
      expiredAccounts.push(e);
    }
  });
};

const getLifetimeValue = (stats, mrr, churn, currencies) => {
  const comCount = stats.international.count;
  const huCount = stats.hungary.count;
  const allCount = comCount + huCount;
  return {
    com: mrr.com / comCount / churn.com,
    hu: mrr.hu / huCount / churn.hu,
    all: (mrr.com * currencies.usd + mrr.hu) / allCount / churn.all,
  };
};

const includesPackage = (calcMrr, pkg) => calcMrr.sku.toLowerCase().includes(pkg);

const mrrToUsd = ({ com, hu }) => {
  return com + hu / 250;
};

const ACCOUNT_SEGMENTS = {
  All: {
    isInSegment: (_) => true,
  },
  Agency: {
    isInSegment: (calcMrr) => {
      let isExcluded = false;
      let isSpecial = false;
      if (calcMrr.email != null) {
        isExcluded =
          calcMrr.email.indexOf('stormmedi') > -1 ||
          calcMrr.email.indexOf('<EMAIL>') > -1 ||
          calcMrr.email.indexOf('<EMAIL>') > -1 ||
          calcMrr.email.indexOf('<EMAIL>') > -1 ||
          calcMrr.email.indexOf('<EMAIL>') > -1;
        isSpecial = ['<EMAIL>', '<EMAIL>'].includes(
          calcMrr.email,
        );
      }
      return isSpecial || (calcMrr.accountType === 'agency' && !isExcluded);
    },
  },
  FullService: {
    // eslint-disable-next-line prettier/prettier
    isInSegment: (_, tags) =>
      tags &&
      !!tags.filter((tag) =>
        ['full-service', 'fullservice', 'full service'].includes(tag.toLowerCase()),
      ).length,
  },
  Master: {
    isInSegment: (calcMrr) => calcMrr.isMidMarket && includesPackage(calcMrr, 'master'),
  },
  Micro: {
    isInSegment: (calcMrr) => {
      return mrrToUsd(calcMrr) <= 60;
    },
  },
  MicroSmb: {
    isInSegment: (calcMrr) => {
      const usd = mrrToUsd(calcMrr);
      return usd > 60 && usd < 160;
    },
  },
  SmbPlus: {
    isInSegment: (calcMrr) => {
      const usd = mrrToUsd(calcMrr);
      return usd > 160;
    },
  },
  Mi: {
    isInSegment: (calcMrr) => {
      return calcMrr.sku.includes('ENTERPRISE_MI');
    },
  },
  Midmarket: {
    isInSegment: (calcMrr) => calcMrr.isMidMarket,
  },
  Shopify: {
    isInSegment: (calcMrr) => calcMrr.isShopify,
  },
  Shoprenter: {
    isInSegment: (calcMrr) => calcMrr.isShoprenter,
  },
  UnasPlatform: {
    isInSegment: (calcMrr) => calcMrr.isUnasPlatform,
  },
  WoocommercePlatform: {
    isInSegment: (calcMrr) => calcMrr.isWoocommercePlatform,
  },
  MagentoPlatform: {
    isInSegment: (calcMrr) => calcMrr.isMagentoPlatform,
  },
  GomagPlatform: {
    isInSegment: (calcMrr) => calcMrr.isGomagPlatform,
  },
  RomanianDomain: {
    isInSegment: (calcMrr) => calcMrr.hasRomanianDomain,
  },
  Smb: {
    isInSegment: (calcMrr) => !calcMrr.isMidMarket,
  },
};

const addExtraPackgeInfoForPeriod = async (_, diff, type) => {
  const ids = diff[`${type}Accounts`].map((e) => e.customerId);
  if (ids.length === 0) {
    return;
  }

  ids.forEach((id) => {
    const packageInfo = diff[`${type}Accounts`].find((e) => e.customerId === id).packageInfo;
    // console.log('@@id', type, id);
    const current = packageInfo.current;
    packageInfo.min = current.interval_start;
    packageInfo.max = current.interval_start;

    packageInfo.currentPackage = {
      omId: current.optimonkId,
      sku: current.sku,
      dateCreated: current.intervalStart,
    };
    // not good
    packageInfo.lastPackage = {
      omId: current.optimonkId,
      sku: current.sku,
      dateCreated: current.intervalStart,
    };
  });
};

const diffMrr = (prevMRR, currentMRR) => {
  const isSamePackage = prevMRR.sku === currentMRR.sku;
  const isPriceEqual = prevMRR.com === currentMRR.com && prevMRR.hu === currentMRR.hu;
  const isDowngrade = prevMRR.com > currentMRR.com || prevMRR.hu > currentMRR.hu;

  if (isSamePackage || isPriceEqual) {
    return { type: 'equal' };
  }

  if (isDowngrade) {
    return {
      type: 'downgrade',
      com: prevMRR.com - currentMRR.com,
      hu: prevMRR.hu - currentMRR.hu,
    };
  }

  return {
    type: 'upgrade',
    com: currentMRR.com - prevMRR.com,
    hu: currentMRR.hu - prevMRR.hu,
  };
};

const diff = (arr1, arr2, excludeOldAccounts = false) => {
  return arr1.filter((e) => !arr2.includes(e) && (excludeOldAccounts ? e > 50000 : true));
};

const concat = (arrays) => {
  // eslint-disable-next-line no-useless-call
  return [].concat.apply([], [...arrays]);
};

const convertToMysqlDate = (value) => {
  return moment(value).format('YYYY-MM-DD');
};

const accountToPriceMap = (account, coupons, platformAccountIds) => {
  const sku = account.sku;
  const isCom = sku.indexOf('-EN-') > -1 || sku.indexOf('-DE-') > -1;
  // todo const price = foundPackage.price * discountModifier;
  const period = parseInt(sku.substring(sku.lastIndexOf('-') + 1), 10);
  const price = account.price;
  let discountModifier = 1;
  let discount = 0;
  let discountName = 'none';

  if (account.coupon_id > 0 && coupons[account.coupon_id]) {
    const coupon = coupons[account.coupon_id];
    discount = coupon.discount;
    discountModifier = (100 - coupon.discount) / 100;
    discountName = coupon.name;
  }

  const isShopify = platformAccountIds?.shopify?.[account.optimonk_id] === true;
  const isShoprenter = platformAccountIds?.shoprenter?.[account.optimonk_id] === true;

  return {
    customerId: account.customer_id,
    optimonkId: account.optimonk_id,
    email: account.email,
    accountType: 'normal',
    isShoprenter,
    isShopify,
    isShopifyPay: account.payment_method_code === 'shopify',
    discount,
    discountModifier,
    discountName,
    price,
    sku,
    langCode: account.currency === 'HUF' ? 'HU' : 'EN',
    platformModifier: 1, // Legacy Shopify
    isMidMarket: isMidMarketByPrice(period, price),
    dateCreated: account.interval_start, // kell?
    intervalStart: account.interval_start,
    intervalEnd: account.interval_end,
    com: isCom ? (account.price / period / 250) * discountModifier : 0,
    hu: isCom ? 0 : (account.price / period) * discountModifier,
  };
};

const getAccountDiffStats = async (accColl, from, to, coupons, addAccounts = false) => {
  const fromStr = convertToMysqlDate(from);
  const toStr = convertToMysqlDate(to);

  const [
    fromAccountsResult,
    toAccountsResults,
    allActiveAccountIdResult,
    leadCount,
    platformAccountIds,
  ] =
    /* eslint-disable */
  await Promise.all([
    salesMysql.getOrderIntervals(fromStr),
    salesMysql.getOrderIntervals(toStr),
    salesMysql.getAllActiveAccountsUntil(fromStr),
    getLeadCountByRegion(accColl, from, to),
    getShopPlatformAccountIds()
  ]);
  const fromAccounts = fromAccountsResult.map(e => parseInt(e.customer_id, 10));
  const toAccounts = toAccountsResults.map(e => parseInt(e.customer_id, 10));
  const allActiveAccountIds = allActiveAccountIdResult.map(e => parseInt(e.customer_id, 10));

  const allNewAccounts = diff(toAccounts, fromAccounts);
  const newAccounts = diff(allNewAccounts, allActiveAccountIds, true);
  const reactivedAccounts = diff(allNewAccounts, newAccounts);
  const canceledAccounts = diff(fromAccounts, toAccounts);
  const changedAccount = concat([newAccounts, reactivedAccounts]);
  const runningAccounts = diff(toAccounts, changedAccount);
  // get all ids for region split
  const idSet = new Set();
  fromAccounts.forEach((e) => idSet.add(e));
  toAccounts.forEach((e) => idSet.add(e));

  const priceMap = {};
  const prevPriceMap = {};
  const allAccounts = [];
  toAccountsResults.forEach((account) => {
    priceMap[account.customer_id] = accountToPriceMap(account, coupons, platformAccountIds);
  });

  fromAccountsResult.forEach((account) => {
    const prevMap = accountToPriceMap(account, coupons, platformAccountIds);
    if (!priceMap[account.customer_id]) {
      priceMap[account.customer_id] = prevMap;
    }
    prevPriceMap[account.customer_id] = prevMap;
  });

  return {
    leadCount,
    accounts: addAccounts ? allAccounts || [] : [],
    new: {
      accountIds: newAccounts,
    },
    reactivate: {
      accountIds: reactivedAccounts,
    },
    cancel: {
      accountIds: canceledAccounts,
    },
    priceMap,
    prevPriceMap,
    runningAccounts,
    fromActiveAccountCount: fromAccounts.length,
    from,
    toActiveAccountCount: toAccounts.length,
    toActiveAccount: {
      accountIds: toAccounts,
    },
    to,
  };
};

const getAccountDiffStatsWithCache = async (accColl, from, to, coupons, addAccounts = false) => {
  const cacheColl = getCollectionRef('backoffice_cache');
  const isToday = moment(to).isSame(new Date(), 'day');

  const cacheKey = `diffStat-${convertToMysqlDate(from)}_${convertToMysqlDate(to)}`;
  const cacheRecord = await cacheColl.findOne({ key: cacheKey });

  // don't return if it is today, keep refreshing cache
  if (cacheRecord && !isToday) {
    return cacheRecord.value;
  }

  const value = await getAccountDiffStats(accColl, from, to, coupons, addAccounts);
  cacheColl.updateOne(
    { key: cacheKey },
    { $set: { value, generatedAt: new Date() } },
    { upsert: true },
  );
  return value;
};

const getAccountDiffs = async (
  accColl,
  _,
  diffUnit,
  numberOfDiffs,
  coupons,
  fromDate = new Date(),
  detailType,
  accountsWithTags,
) => {
  const dateRanges = [];
  // add previous weeks
  diffUnit = diffUnit !== 'month' ? 'week' : 'month';
  // use jan 31 instead of feb 1
  const subtractDay = diffUnit === 'month' ? 1 : 0;
  const diffUnitPlural = `${diffUnit}s`;
  const current = moment(fromDate).startOf(diffUnit).subtract(numberOfDiffs, diffUnitPlural);
  for (let i = numberOfDiffs; i >= 0; i--) {
    const from = new Date(current.clone().subtract(subtractDay, 'day').format('YYYY-MM-DD'));
    let to = new Date(
      current.add(1, diffUnitPlural).clone().subtract(subtractDay, 'day').format('YYYY-MM-DD'),
    );
    // If future use today -> partial month
    if (to.getTime() > new Date().getTime()) {
      to = new Date(moment().format('YYYY-MM-DD'));
    }
    dateRanges.push({
      from,
      to,
    });
  }

  const promises = dateRanges.map((e) =>
    getAccountDiffStatsWithCache(accColl, e.from, e.to, coupons, true),
  );

  const diffs = await Promise.all(promises);
  const currencies = await getCurrencies();
  let i = 0;
  for (const diff of diffs) {
    ['new', 'reactivate', 'cancel', 'toActiveAccount'].forEach((accType) => {
      const accountIds = diff[accType].accountIds;
      const ids = {};
      diff[`${accType}Accounts`] = [];
      accountIds.forEach((id) => {
        const foundPackage = diff.priceMap[id];
        const accountTags = accountsWithTags[id];
        // console.log('@@id', id);
        const calcMrr = foundPackage;
        Object.keys(ACCOUNT_SEGMENTS).forEach((key) => {
          const mrrKey = `${accType + key}Mrr`;
          if (diff[mrrKey] == null)
            diff[mrrKey] = {
              com: 0,
              hu: 0,
            };
          if (ids[key] == null) ids[key] = [];

          if (ACCOUNT_SEGMENTS[key].isInSegment(calcMrr, accountTags)) {
            // console.log('@@key', key, id, calcMrr);
            diff[mrrKey].com += calcMrr.com;
            diff[mrrKey].hu += calcMrr.hu;
            ids[key].push(id);

            if (detailType && detailType.segment === key && detailType.type === accType) {
              const packageInfo = {
                current: calcMrr,
              };
              diff[`${accType}Accounts`].push({
                ...calcMrr,
                packageInfo,
              });
            }
          }
        });
      });

      Object.keys(ACCOUNT_SEGMENTS).forEach((key) => {
        // on first day of month, when no accounts available add default value
        const mrrKey = `${accType + key}Mrr`;
        if (diff[mrrKey] == null)
          diff[mrrKey] = {
            com: 0,
            hu: 0,
          };

        diff[accType + key] = splitIdsByRegion(ids[key] || [], diff.priceMap);
      });
    });

    const accountIds = diffs[i].runningAccounts;
    if (detailType && diff[`${detailType.type}Accounts`] == null) {
      diff[`${detailType.type}Accounts`] = [];
    }
    const ids = {};
    accountIds.forEach((id) => {
      const currentMRR = getAccountMRRs(diff.priceMap[id], currencies, diff.from);
      const prevMRR = getAccountMRRs(diff.prevPriceMap[id], currencies, diff.from);
      const mrrDiff = diffMrr(prevMRR, currentMRR);
      if (mrrDiff.type !== 'equal') {
        const mrrKey = `${mrrDiff.type}Mrr`;
        if (diff[mrrKey] == null)
          diff[mrrKey] = {
            com: 0,
            hu: 0,
          };
        if (ids[mrrDiff.type] == null) ids[mrrDiff.type] = [];

        diff[mrrKey].com += mrrDiff.com;
        diff[mrrKey].hu += mrrDiff.hu;
        ids[mrrDiff.type].push(id);

        Object.keys(ACCOUNT_SEGMENTS).forEach((key) => {
          const idKey = mrrDiff.type + key;
          const mrrKey = `${idKey}Mrr`;
          if (diff[mrrKey] == null)
            diff[mrrKey] = {
              com: 0,
              hu: 0,
            };
          if (ids[idKey] == null) ids[idKey] = [];

          const inInSegment =
            mrrDiff.type === 'downgrade'
              ? ACCOUNT_SEGMENTS[key].isInSegment(prevMRR)
              : ACCOUNT_SEGMENTS[key].isInSegment(currentMRR);
          if (inInSegment) {
            diff[mrrKey].com += mrrDiff.com;
            diff[mrrKey].hu += mrrDiff.hu;
            ids[idKey].push(id);
            if (detailType && detailType.segment === key && detailType.type === mrrDiff.type) {
              const packageInfo = {
                prev: prevMRR,
                current: currentMRR,
              };
              const account = {
                ...diff.priceMap[id],
              };
              account.packageInfo = packageInfo;
              diff[`${detailType.type}Accounts`].push(account);
            }
          }
        });
      }
    });

    const diffKeys = ['downgrade', 'upgrade'];
    diffKeys.forEach((diffKey) => {
      Object.keys(ACCOUNT_SEGMENTS).forEach((key) => {
        const segmentKey = diffKey + key;
        const segmentMrrKey = `${segmentKey}Mrr`;
        if (diff[segmentMrrKey] == null)
          diff[segmentMrrKey] = {
            com: 0,
            hu: 0,
          };
        diff[segmentKey] = splitIdsByRegion(ids[segmentKey] || [], diff.priceMap);
      });
    });

    i++;
  }

  // remove priceMap from result
  diffs.forEach((d) => {
    delete d.priceMap;
    delete d.prevPriceMap;
    delete d.accounts;
    delete d.runningAccounts;
    const keys = Object.keys(d);
    keys.forEach((k) => {
      delete d[k].accountIds;
    });
  });

  return diffs;
};

const getTodaysAccountsForExport = async (accColl, activeAccColl, accountIds = []) => {
  const to = new Date(moment().format('YYYY-MM-DD'));
  if (accountIds.length === 0) {
    const activeAccounts = await activeAccColl.findOne(
      {
        day: to,
      },
      {
        project: {
          accountIds: 1,
        },
      },
    );
    accountIds = activeAccounts.accountIds;
  }

  const [accounts, priceMap, packagesMap] = await Promise.all([
    getAccountsWithRegion(accColl, accountIds),
    salesMysql.getAccountPackages(to),
    packagesToMap(),
  ]);
  const currencies = getCurrencies();
  accounts.forEach((a) => {
    if (!priceMap[a.databaseId]) {
      priceMap[a.databaseId] = getPackageInfo(a, packagesMap);
    }
    priceMap[a.databaseId].isShopifyPay = a.isShopifyPay;
    priceMap[a.databaseId].discountModifier = getDiscountModifier(a);
    a.mrrInfo = getAccountMRRs(priceMap[a.databaseId], currencies);
  });
  return accounts;
};

const getCanceledAccountsForExport = async (accColl, activeAccColl, from, to, region) => {
  const [allAccounts, { accountIds: toAccounts }] = await Promise.all([
    getAllActiveAccountsBetween(activeAccColl, {
      from,
      until: to,
    }),
    activeAccColl.findOne(
      {
        day: to,
      },
      {
        project: {
          accountIds: 1,
        },
      },
    ),
  ]);

  const canceledAccounts = diff(allAccounts, toAccounts);
  // eslint-disable-next-line prefer-const
  let [accounts, priceMap, packagesMap] = await Promise.all([
    getAccountsWithRegion(accColl, canceledAccounts),
    salesMysql.getAccountPackages(to),
    packagesToMap(),
  ]);
  const currencies = getCurrencies();
  if (region && region === 'hu') {
    accounts = accounts.filter((a) => a.region === 'Hungary');
  }

  if (region && region === 'com') {
    accounts = accounts.filter((a) => a.region !== 'Hungary');
  }

  accounts.forEach((a) => {
    if (!priceMap[a.databaseId]) {
      priceMap[a.databaseId] = getPackageInfo(a, packagesMap);
    }
    priceMap[a.databaseId].isShopifyPay = a.isShopifyPay;
    priceMap[a.databaseId].discountModifier = getDiscountModifier(a);
    a.mrrInfo = getAccountMRRs(priceMap[a.databaseId], currencies);
  });
  return accounts;
};

const getSpecAccountsForExport = async (accColl, specAccountIds) => {
  // eslint-disable-next-line prefer-const
  let [accounts, priceMap, packagesMap] = await Promise.all([
    getAccountsWithRegion(accColl, specAccountIds),
    salesMysql.getAccountPackages(new Date()),
    packagesToMap(),
  ]);
  const currencies = getCurrencies();
  accounts.forEach((a) => {
    if (!priceMap[a.databaseId]) {
      priceMap[a.databaseId] = getPackageInfo(a, packagesMap);
    }
    priceMap[a.databaseId].isShopifyPay = a.isShopifyPay;
    priceMap[a.databaseId].discountModifier = getDiscountModifier(a);
    a.mrrInfo = getAccountMRRs(priceMap[a.databaseId], currencies);
  });
  return accounts;
};

const getStatsWithSubaccounts = async (db, account) => {
  let dbIds = [];
  if (account.type === 'agency') {
    dbIds = await getSubAccountDatabaseIds(db, account.subAccounts);
  } else {
    dbIds = [account.databaseId];
  }

  const promises = dbIds.map((dbId) => getAccountStatistics(db, dbId, account.dateExpires));
  let result = await Promise.all(promises);
  result = result.flat(4);

  const stats = {
    impr: 0,
    conv: 0,
  };
  result.forEach((e) => {
    stats.impr += e.impr;
    stats.conv += e.conv;
  });
  return stats;
};

const getCampaignStatsWithSubaccounts = async (db, account) => {
  let dbIds = [];
  if (account.type === 'agency') {
    dbIds = await getSubAccountDatabaseIds(db, account.subAccounts);
  } else {
    dbIds = [account.databaseId];
  }

  const promises = dbIds.map((dbId) => getAccountCampaigStatistics(db, dbId));
  let result = await Promise.all(promises);
  result = result.flat(4);
  const stats = {
    active: 0,
    all: 0,
  };
  result.forEach((e) => {
    stats.active += e.active;
    stats.all += e.all;
  });
  return stats;
};

const getAccountWithMrr = async (accColl, activeAccColl) => {
  const [{ accountIds }] = await activeAccColl
    .find(
      {},
      {
        project: {
          accountIds: 1,
        },
      },
    )
    .limit(1)
    .sort({
      day: -1,
    })
    .toArray();
  const [accounts, paymentMethods, lastOrders] = await Promise.all([
    getAccountsWithRegion(accColl, accountIds),
    salesMysql.getCustomerPaymentMethods(),
    salesMysql.getLastOrderValues(),
  ]);
  const currencies = getCurrencies();
  const packagesMap = packagesToMap();
  const realAccountPaidDate = moment().subtract(15, 'days').startOf('day');
  accounts.forEach((account) => {
    let lastOrderValue = '';
    if (lastOrders[account.databaseId]) {
      const currConv = account.region === 'Hungary' ? 1 : 250;
      lastOrderValue = lastOrders[account.databaseId].value / account.period / currConv;
      account.orderId = lastOrders[account.databaseId].orderId;
      account.orderAt = lastOrders[account.databaseId].orderAt;
      account.orderPaymentMethod = lastOrders[account.databaseId].orderPaymentMethod;
    }

    const { com, hu, isMidMarket } = getAccountMrr(account, packagesMap, currencies);
    account.paymentMethod = paymentMethods[account.databaseId]
      ? paymentMethods[account.databaseId].payment_method
      : 'unknown';
    account.mrrHu = hu;
    account.mrrCom = com;
    account.usedMrr = com || hu;
    account.lastOrderValue = lastOrderValue;
    account.isMidMarket = isMidMarket;
    account.excluded = !moment(account.datePaid).isAfter(realAccountPaidDate);
  });
  return accounts;
};

const getStats = (accounts) => {
  const mrr = {
    com: 0,
    hu: 0,
    all: 0,
  };
  const stats = {
    all: region(),
    hungary: region(),
    international: region(),
  };

  for (const account of accounts) {
    stats.all.count++;
    let region;
    switch (account.region) {
      case 'Hungary':
        stats.hungary.count++;
        region = 'hungary';
        break;
      default:
        stats.international.count++;
        region = 'international';
        break;
    }
    const packageName = formatOldPackageNames(account.package);
    const periodName = formatPeriod(account.period);
    stats.all.packages[packageName]++;
    stats.all.periods[periodName]++;
    stats[region].packages[packageName]++;
    stats[region].periods[periodName]++;
  }

  return {
    stats,
    mrr,
  };
};

const updateActiveAccountsStat = async function (accColl, activeAccColl) {
  const day = new Date(moment().format('YYYY-MM-DD'));
  const hasAccountStats = false; // (await activeAccColl.countDocuments({ day })) > 0

  if (hasAccountStats) {
    return;
  }

  const accounts = await getActiveAccounts(accColl);
  const accountIds = [];
  const regions = {
    all: 0,
    hu: 0,
    en: 0,
    de: 0,
  };

  accounts.forEach((e) => {
    regions.all++;
    switch (e.region) {
      case 'Hungary':
        regions.hu++;
        break;
      case 'USA':
        regions.en++;
        break;
      case 'Germany':
        regions.de++;
        break;
    }
    accountIds.push(e.databaseId);
  });
  accountIds.sort((a, b) => a - b);
  return activeAccColl.updateOne(
    {
      day,
    },
    {
      $set: {
        day,
        regions,
        accountIds,
      },
    },
    {
      upsert: true,
    },
  );
};

const getImpressionStatsFromDDS = async (coll, days = 30) => {
  const impressionsStats = {
    free: {
      com: 0,
      hu: 0,
      all: 0,
    },
    trial: {
      com: 0,
      hu: 0,
      all: 0,
    },
    paying: {
      com: 0,
      hu: 0,
      all: 0,
    },
  };
  const _30DaysAgo = new Date(moment().subtract(days, 'days').format('YYYY-MM-DD'));
  const accountsWithImpressions = await coll.aggregate([
    {
      $match: {
        $and: [
          {
            $or: [
              {
                package: {
                  $eq: 'FREE',
                },
                createdAt: {
                  $gte: _30DaysAgo,
                },
              },
              {
                package: {
                  $nin: ['BELSO', 'FREE'],
                },
              },
            ],
          },
          {
            $or: [
              {
                'statistics.last30DayStats.impressions': {
                  $gte: 100,
                },
              },
              {
                'statistics.last30DayStats.conversions': {
                  $gte: 10,
                },
              },
            ],
          },
        ],
      },
    },
    {
      $addFields: {
        user: {
          $arrayElemAt: ['$user', 0],
        },
      },
    },
    {
      $group: {
        _id: {
          package: '$package',
          region: '$user.region',
        },
        count: {
          $sum: 1,
        },
      },
    },
    {
      $sort: {
        count: -1,
      },
    },
  ]);

  accountsWithImpressions.forEach((e) => {
    const { _id: account, count } = e;
    let simplifiedPackage = formatOldPackageNames(account.package);
    if (!['free', 'trial'].includes(simplifiedPackage)) {
      simplifiedPackage = 'paying';
    }
    const simplifiedRegion = account.region === 'Hungary' ? 'hu' : 'com';
    impressionsStats[simplifiedPackage][simplifiedRegion] += count;
    impressionsStats[simplifiedPackage].all += count;
  });
  return impressionsStats;
};

module.exports = {
  addExtraPackgeInfoForPeriod,
  getAccountDiffs,
  getAccountDiffStats,
  getAccountDiffStatsWithCache,
  getAccountMRRs,
  getAccountWithMrr,
  getCampaignStatsWithSubaccounts,
  getCanceledAccountsForExport,
  getCurrencies,
  getDiscountModifier,
  getImpressionStatsFromDDS,
  getLifetimeValue,
  getSpecAccountsForExport,
  getStats,
  getStatsWithSubaccounts,
  getTodaysAccountsForExport,
  packages,
  packagesToMap,
  periods,
  seperateActiveAccounts,
  updateActiveAccountsStat,
  accountToPriceMap,
};

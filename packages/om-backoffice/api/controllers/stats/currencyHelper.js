const getExchangeRates = async () => {
  return [
    { day: new Date('2020-09-01'), value: 296.53 },
    { day: new Date('2020-10-01'), value: 308.62 },
    { day: new Date('2020-11-02'), value: 315.14 },
    { day: new Date('2020-12-01'), value: 298.28 },
    { day: new Date('2021-01-04'), value: 293.58 },
    { day: new Date('2021-02-01'), value: 294.86 },
    { day: new Date('2021-03-01'), value: 301.37 },
    { day: new Date('2021-04-01'), value: 308.6 },
    { day: new Date('2021-05-03'), value: 298.98 },
    { day: new Date('2021-06-01'), value: 283.94 },
    { day: new Date('2021-07-01'), value: 296.59 },
    { day: new Date('2021-08-03'), value: 298.86 },
    { day: new Date('2021-09-01'), value: 294.88 },
    { day: new Date('2021-10-01'), value: 310.23 },
    { day: new Date('2021-11-03'), value: 309.75 },
    { day: new Date('2021-11-03'), value: 309.75 },
    { day: new Date('2021-12-01'), value: 321.69 },
    { day: new Date('2022-01-03'), value: 323.87 },
    { day: new Date('2022-02-01'), value: 315.69 },
    { day: new Date('2022-03-01'), value: 332.64 },
    { day: new Date('2022-04-01'), value: 332.27 },
    { day: new Date('2022-05-03'), value: 364.17 },
    { day: new Date('2022-06-01'), value: 340.43 },
    { day: new Date('2022-07-01'), value: 381.24 },
    { day: new Date('2022-08-01'), value: 393.21 },
    { day: new Date('2022-09-01'), value: 399.76 },
    { day: new Date('2022-10-03'), value: 430.65 },
    { day: new Date('2022-10-28'), value: 414.46 },
    { day: new Date('2022-11-02'), value: 412.22 },
    { day: new Date('2022-11-16'), value: 390.72 },
    { day: new Date('2022-12-01'), value: 392.68 },
    { day: new Date('2022-12-15'), value: 381.97 },
    { day: new Date('2023-01-02'), value: 375.18 },
    { day: new Date('2023-01-16'), value: 368.71 },
    { day: new Date('2023-01-19'), value: 365.26 },
    { day: new Date('2023-02-01'), value: 358.48 },
    { day: new Date('2023-02-15'), value: 353.63 },
    { day: new Date('2023-03-01'), value: 353.48 },
    { day: new Date('2023-03-14'), value: 368.42 },
    { day: new Date('2023-04-03'), value: 350.7 },
    { day: new Date('2023-04-17'), value: 340.26 },
    { day: new Date('2023-05-02'), value: 338.84 },
    { day: new Date('2023-05-15'), value: 339.97 },
    { day: new Date('2023-06-01'), value: 346.73 },
    { day: new Date('2023-06-15'), value: 344.92 },
    { day: new Date('2023-07-03'), value: 343.27 },
    { day: new Date('2023-07-14'), value: 333.93 },
    { day: new Date('2023-07-28'), value: 350.21 },
    { day: new Date('2023-08-14'), value: 349.58 },
    { day: new Date('2023-09-01'), value: 353.95 },
    { day: new Date('2023-09-15'), value: 360.1 },
    { day: new Date('2023-10-02'), value: 366.81 },
    { day: new Date('2023-10-16'), value: 366.92 },
    { day: new Date('2023-11-02'), value: 360.07 },
    { day: new Date('2023-11-10'), value: 352.98 },
    { day: new Date('2023-11-20'), value: 346.61 },
    { day: new Date('2023-12-01'), value: 348.84 },
    { day: new Date('2023-12-15'), value: 347.05 },
    { day: new Date('2024-01-02'), value: 346.91 },
    { day: new Date('2024-01-16'), value: 349.38 },
    { day: new Date('2024-02-01'), value: 356.43 },
    { day: new Date('2024-02-15'), value: 362.55 },
    { day: new Date('2024-03-01'), value: 364.04 },
    { day: new Date('2024-03-18'), value: 361.89 },
    { day: new Date('2024-04-02'), value: 368.33 },
    { day: new Date('2024-04-15'), value: 368.85 },
    { day: new Date('2024-05-02'), value: 364.0 },
    { day: new Date('2024-05-15'), value: 356.69 },
    { day: new Date('2024-05-31'), value: 359.43 },
    { day: new Date('2024-06-17'), value: 369.89 },
    { day: new Date('2024-07-01'), value: 365.33 },
    { day: new Date('2024-07-15'), value: 358.73 },
    { day: new Date('2024-08-01'), value: 367.12 },
    { day: new Date('2024-08-15'), value: 358.55 },
    { day: new Date('2024-09-02'), value: 354.88 },
    { day: new Date('2024-09-16'), value: 354.2 },
    { day: new Date('2024-10-04'), value: 364.33 },
    { day: new Date('2024-10-15'), value: 366.94 },
    { day: new Date('2024-10-31'), value: 375.81 },
    { day: new Date('2024-11-29'), value: 391.3 },
    { day: new Date('2024-12-16'), value: 388.99 },
    { day: new Date('2025-01-02'), value: 397.85 },
    { day: new Date('2025-01-15'), value: 398.84 },
    { day: new Date('2025-01-28'), value: 391.93 },
    { day: new Date('2025-02-17'), value: 384.29 },
    { day: new Date('2025-03-03'), value: 385.4 },
    { day: new Date('2025-03-17'), value: 366.36 },
    { day: new Date('2025-04-01'), value: 372.43 },
    { day: new Date('2025-04-15'), value: 361.29 },
    { day: new Date('2025-04-30'), value: 355.76 },
    { day: new Date('2025-05-15'), value: 359.72 },
    { day: new Date('2025-06-02'), value: 352.85 },
  ];
};

const getExchangeRateByDate = async (day) => {
  const exchangeRates = await getExchangeRates();

  let index = exchangeRates.length - 1;
  while (exchangeRates[index].day > day && index > 0) {
    index--;
  }

  return exchangeRates[index].value;
};

module.exports = {
  getExchangeRateByDate,
};

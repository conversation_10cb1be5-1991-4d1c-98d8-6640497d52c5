const { ObjectID } = require('mongodb');
const moment = require('moment');

const INTERNAL_PACKAGES = ['BELSO', 'FREE', 'DEMO', 'TRIAL'];

const getMidmarketLeadIds = async function (coll, activeAccColl, from) {
  const midmarketLeadIds = await coll
    .find({
      createdAt: { $gte: from },
      'settings.qualifications.stage': { $in: ['scaling', 'established'] },
    })
    .project({ databaseId: 1 })
    .toArray();
  return midmarketLeadIds.map((e) => e.databaseId);
};

const getActiveAccountMinMaxDays = async function (coll, ids, from, to) {
  let accounts = await coll
    .aggregate([
      { $match: { day: { $gte: from, $lte: to } } },
      { $unwind: '$accountIds' },
      {
        $group: {
          _id: '$accountIds',
          min: { $min: '$day' },
          max: { $max: '$day' },
        },
      },
    ])
    .toArray();
  accounts = accounts.filter((e) => ids.includes(e._id));
  const obj = {};
  accounts.forEach((e) => {
    obj[e._id] = {
      min: e.min,
      max: e.max,
    };
  });
  return obj;
};

const getActiveAccounts = async function (coll, expiredDays = 0) {
  const dateExpiresDate = new Date(moment().subtract(expiredDays, 'days').format('YYYY-MM-DD'));
  const accounts = await coll
    .aggregate([
      {
        $match: {
          name: { $not: /@optimonk/ },
          'billing.package': { $nin: INTERNAL_PACKAGES },
          'billing.dateExpires': { $gte: dateExpiresDate },
          type: { $ne: 'sub' },
        },
      },
      {
        $lookup: {
          from: 'master_logins',
          foreignField: '_id',
          localField: 'users.0.loginId',
          as: 'logins',
        },
      },
      { $addFields: { login: { $arrayElemAt: ['$logins', 0] } } },
      {
        $project: {
          users: 1,
          databaseId: 1,
          dateExpires: '$billing.dateExpires',
          datePaid: '$billing.datePaid',
          package: '$billing.package',
          period: '$billing.period',
          email: '$login.email',
          region: '$login.region',
          shopifyPay: '$settings.shops.pay',
          shopTypes: '$settings.shops.type',
        },
      },
    ])
    .toArray();
  // set shopifyPay
  accounts.forEach((e) => {
    const isShopifyPay =
      e.shopifyPay && e.shopifyPay.find((p) => p === '1' || p === 1) !== undefined;
    const isShoprenter = e.shopTypes && e.shopTypes.includes('shoprenter');
    e.isShopifyPay = isShopifyPay;
    e.isShoprenter = isShoprenter;
  });
  return accounts;
};

const getAccountsWithRegion = async (coll, ids) => {
  const accounts = await coll
    .aggregate([
      { $match: { databaseId: { $in: ids } } },
      {
        $lookup: {
          from: 'master_logins',
          foreignField: '_id',
          localField: 'users.0.loginId',
          as: 'logins',
        },
      },
      {
        $project: {
          databaseId: 1,
          name: 1,
          type: 1,
          subAccounts: 1,
          createdAt: 1,
          billing: 1,
          users: 1,
          domains: '$settings.domains',
          email: '$logins.email',
          firstName: '$logins.firstName',
          lastName: '$logins.lastName',
          regions: '$logins.region',
          lastLogin: '$logins.lastLogin',
          phone: '$logins.phoneNumber',
          shops: '$settings.shops',
        },
      },
    ])
    .toArray();

  const result = accounts.map((a) => {
    let isShopify = false;
    let isShoprenter = false;
    let isShopifyPay = false;
    let isUnas = false;
    let isWoocommerce = false;
    let isMagento = false;
    let isGomag = false;
    let hasRomanianDomain = false;
    const domains = a.domains || [];
    let platforms = domains.map((d) => d.platform).filter((e) => e && e.length > 3);
    platforms = Array.from(new Set(platforms)).join(', ');
    platforms = platforms.replace('custom', 'other');

    // Check domain platforms for Unas, Woocommerce, Magento, and Gomag
    isUnas = domains.some((d) => d.platform === 'unas');
    isWoocommerce = domains.some((d) => d.platform === 'woocommerce');
    isMagento = domains.some((d) => d.platform === 'magento');
    isGomag = domains.some((d) => d.platform === 'gomag');

    // Check for Romanian domains (.ro)
    hasRomanianDomain = domains.some((d) => d.domain && d.domain.endsWith('.ro'));

    if (a.shops) {
      const shopifyShops = a.shops.filter((e) => e.type === 'shopify');
      const shoprenterShops = a.shops.filter((e) => e.type === 'shoprenter');
      const payingShops = shopifyShops.filter((e) => e.pay === '1' || e.pay === 1);
      isShopify = shopifyShops.length > 0;
      isShoprenter = shoprenterShops.length > 0;
      isShopifyPay = payingShops.length > 0;
    }

    let ownerId = '';
    if (a.users != null) {
      // ToDo remove this hotfix
      const owner = a.users.find((e) => e.role === 'owner');
      ownerId = owner ? owner.loginId : null;
    }

    return {
      _id: a._id,
      ownerId,
      databaseId: a.databaseId,
      email: a.email[0],
      firstName: a.firstName[0],
      lastName: a.lastName[0],
      type: a.type,
      subAccounts: a.subAccounts,
      package: a.billing.package,
      period: a.billing.period,
      datePaid: a.billing.datePaid,
      dateExpires: a.billing.dateExpires,
      domains: domains.map((d) => d.domain).join(', '),
      platforms,
      createdAt: a.createdAt,
      lastLogin: a.lastLogin[0],
      region: a.regions[0],
      phone: a.phone.join(', '),
      isShopify,
      isShoprenter,
      isUnasPlatform: isUnas,
      isWoocommercePlatform: isWoocommerce,
      isMagentoPlatform: isMagento,
      isGomagPlatform: isGomag,
      hasRomanianDomain,
      isShopifyPay,
    };
  });
  return result;
};

const getAllActiveAccountsBetween = async function (activeAccModel, { from, until }) {
  const match = { accountIds: { $ne: [] }, day: { $lte: until } };
  if (from) {
    match.day.$gte = from;
  }
  const result = await activeAccModel
    .aggregate([
      { $match: match },
      { $unwind: '$accountIds' },
      { $group: { _id: null, accountIds: { $addToSet: '$accountIds' } } },
    ])
    .toArray();
  return result ? result[0].accountIds : [];
};

const getSubAccountDatabaseIds = async (db, subAccounts) => {
  subAccounts = subAccounts.map((a) => ObjectID(a));
  const results = await db
    .collection(`master_accounts`)
    .find({ _id: { $in: subAccounts } })
    .project({ databaseId: 1 })
    .toArray();
  return results.map((e) => e.databaseId);
};

const getAccountStatistics = async (db, dbId, expires) => {
  const stillActive = moment().isBefore(expires);
  const to = stillActive ? new Date() : new Date(expires);
  const from = moment(to).subtract(91, 'days').toDate();

  const results = await db
    .collection('user_statistics')
    .aggregate([
      { $match: { databaseId: dbId, period: { $gte: from, $lte: to } } },
      {
        $group: {
          _id: null,
          impr: { $sum: '$impressions' },
          conv: { $sum: '$conversions' },
        },
      },
    ])
    .toArray();
  return results;
};

const getAccountCampaigStatistics = async (db, dbId) => {
  const results = await db
    .collection(`${dbId}_campaigns`)
    .aggregate([{ $group: { _id: '$status', count: { $sum: 1 } } }])
    .toArray();
  if (results.length > 0) {
    const activeResult = results.find((c) => c._id === 'active');
    const active = activeResult ? activeResult.count : 0;
    const inactiveResult = results.find((c) => c._id === 'inactive');
    const inactive = inactiveResult ? inactiveResult.count : 0;
    return {
      active,
      all: active + inactive,
    };
  }
  return {
    active: 0,
    all: 0,
  };
};

const getLeadCountByRegion = async (coll, from, to) => {
  const accounts = await coll.aggregate([
    { $match: { createdAt: { $gte: from, $lte: to } } },
    {
      $lookup: {
        from: 'master_logins',
        foreignField: '_id',
        localField: 'users.0.loginId',
        as: 'logins',
      },
    },
    { $project: { users: 1, region: { $arrayElemAt: ['$logins.region', 0] } } },
    { $group: { _id: '$region', count: { $sum: 1 } } },
  ]);

  const result = { all: 0, com: 0, hu: 0 };
  accounts.forEach((e) => {
    result.all += e.count;
    if (e._id === 'Hungary') {
      result.hu += e.count;
    } else {
      result.com += e.count;
    }
  });
  return result;
};

const getTagsForDatabaseIds = async (coll) => {
  const accountsWithTags = await coll.find({ tags: { $ne: [] } });
  const result = {};
  accountsWithTags.forEach((account) => {
    result[account.databaseId] = account.tags;
  });

  return result;
};

module.exports = {
  getAccountCampaigStatistics,
  getAccountStatistics,
  getAccountsWithRegion,
  getActiveAccounts,
  getActiveAccountMinMaxDays,
  getAllActiveAccountsBetween,
  getMidmarketLeadIds,
  getLeadCountByRegion,
  getSubAccountDatabaseIds,
  getTagsForDatabaseIds,
};

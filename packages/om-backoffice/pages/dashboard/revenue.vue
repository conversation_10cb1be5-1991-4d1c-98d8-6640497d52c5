<template lang="pug">
v-container(fluid)
  v-progress-linear(v-if="isLoading" indeterminate color="#6078FF")
  template(v-if="!isLoading")
    v-snackbar(v-model="showError" :top="true" :timeout="6000" color="red") An Error has occured
      v-btn(color="white" @click="showError = false") Close
    account-mrr-dialog(
      :settings="accountMrrDialogSettings"
      @close="accountMrrDialogSettings.show = false"
      @error="showError = true"
    )
    export-account-dialog(
      :settings="exportAccountDialogSettings"
      @close="exportAccountDialogSettings.show = false"
      @error="showError = true"
    )
    v-row(style="align-items: center")
      v-col(v-for="cardType in topCardTypes" :key="cardType.value")
        v-card(min-height="120px")
          v-card-text
            .caption.text-uppercase {{ cardType.text }}
            template(v-if="cardType.value === 'freeUsers'")
              .subtitle-1.dash-blue {{ getTopCardValue(cardType.value, 'all') }}
            template(v-else-if="cardType.value === 'activity'")
              .caption Paying:
                span.subtitle-2.dash-blue.ml-1 {{ getUserActivity('paying') }}
              .caption Free:
                span.subtitle-2.dash-blue.ml-1 {{ getUserActivity('free') }}
              .caption All Active:
                span.subtitle-2.dash-blue.ml-1 {{ getActiveMerchantCount }}
            template(v-else)
              .subtitle-1.dash-blue {{ getTopCardValue(cardType.value, 'all') }}
              .caption.text-uppercase com: {{ getTopCardValue(cardType.value, 'com') }}
              .caption.text-uppercase hu: {{ getTopCardValue(cardType.value, 'hu') }}
    v-row(style="align-items: center")
      v-col.mr-3
        v-select(v-model="selectedChartDimension" :items="chartDimensions" label="Chart Dimension")
      v-col.mr-3
        v-select(v-model="selectedAccountType" :items="accountTypes" label="Account Type")
      v-col
        v-btn-toggle(v-model="groupBy" mandatory)
          v-btn(value="week" text :max-height="buttonGroupHeight") Weeks
          v-btn(value="month" text :max-height="buttonGroupHeight") Months
      v-col
        v-btn-toggle(v-model="numberOfDiffs" mandatory)
          v-btn(:value="7" text :max-height="buttonGroupHeight") 6
          v-btn(:value="10" text :max-height="buttonGroupHeight") 9
          v-btn(:value="13" text :max-height="buttonGroupHeight") 12
          v-btn(:value="25" text :max-height="buttonGroupHeight") 24
      v-col
        v-btn-toggle(v-model="region" mandatory)
          v-btn(value="all" text :max-height="buttonGroupHeight") ALL
          v-btn(value="com" text :max-height="buttonGroupHeight") Com
          v-btn(value="hu" text :max-height="buttonGroupHeight") Hu
      v-col
        v-btn-toggle(v-model="displayInterval" mandatory)
          v-btn(value="mrr" text :max-height="buttonGroupHeight") MRR
          v-btn(value="arr" text :max-height="buttonGroupHeight") ARR
      v-col
        v-tooltip(bottom)
          template(v-slot:activator="{ on }")
          v-label(color="white") 1 USD = {{ usdHufValue }} Ft
        v-btn-toggle(v-model="currency" mandatory)
          v-btn(value="usd" text :max-height="buttonGroupHeight") USD
            img(height=20 src="~/assets/image/flag_usa.svg")
          v-btn(value="huf" text :max-height="buttonGroupHeight") HUF
            img(height=20 src="~/assets/image/flag_hun.svg")
      v-btn.mr-2(color="primary" :loading="exportLoading" @click="exportPayingAccount") Export Active
      v-btn(color="primary" @click="exportAccountDialogSettings.show = true") Export Canceled
    v-row
      canvas#mrrChart(height="250")
    v-row(fluid)
      v-card.my-2(style="width: 100%")
        v-container(fill-height fluid)
          v-row
            v-data-table(
              :headers="headers"
              :items="chartDimensions"
              :options="{ itemsPerPage: chartDimensions.length }"
              hide-default-footer
              style="width: 100%"
            )
              template(v-slot:item="{ item }")
                tr(:style="getRowStyle(item.value)")
                  td.text-xs-right.capitalize(
                    @click="selectDimension(item.value)"
                    style="cursor: pointer"
                  ) {{ item.text }}
                  td.text-xs-right(
                    v-for="index in diffs.length"
                    :key="index"
                    :style="getTdStyle(item.value)"
                    @click.stop="openAccountModal(item.value, index)"
                  )
                    span(:style="getCellStyle(item.value)") {{ getTableValue(item.value, index) }}
</template>
<script>
  import { saveAs } from 'file-saver';
  import AccountMrrDialog from '@/components/dialog/AccountMrrDialog.vue';
  import ExportAccountDialog from '@/components/dialog/ExportAccountDialog.vue';
  import {
    changeCurr,
    formatNumber,
    formatPercentage,
    shortFormatDay,
    shortFormatMonth,
  } from '@/util/format';

  const _clone = (v) => JSON.parse(JSON.stringify(v));

  const NO_DIALOG_ROWS = [
    'arpu',
    'arr',
    'churn',
    'growth',
    'lead',
    'leadToPay',
    'lifetime',
    'ltv',
    'mrr_churn',
    'net_loss',
    'net_loss_percent',
    'mrr',
    'user',
  ];
  export default {
    components: { AccountMrrDialog, ExportAccountDialog },
    data() {
      return {
        topCardTypes: [
          { text: 'Mrr', value: 'mrr' },
          { text: 'Midmarket Mrr', value: 'midmarketMrr' },
          { text: 'Arr', value: 'arr' },
          { text: 'Midmarket Arr', value: 'midmarketArr' },
          { text: 'Paying Users', value: 'payingUsers' },
          { text: 'Activity', value: 'activity' },
          { text: 'Free Users', value: 'freeUsers' },
        ],
        accountMrrDialogSettings: {
          show: false,
          type: 'new', // property needed for computed to work in dialog
        },
        exportAccountDialogSettings: {
          show: false,
        },
        buttonGroupHeight: 36,
        numberOfDiffs: 10,
        groupBy: 'month',
        selectedChartDimension: 'mrr',
        selectedAccountType: 'all',
        accountTypes: [
          { text: 'All', value: 'all' },
          { text: '<79 USD', value: 'micro' },
          { text: '79-199 USD', value: 'microSmb' },
          { text: '199+ USD', value: 'smbPlus' },
          { text: 'SMB', value: 'smb' },
          { text: 'Midmarket', value: 'midmarket' },
          { text: 'Agency', value: 'agency' },
          { text: 'Master', value: 'master' },
          { text: 'Shopify', value: 'shopify' },
          { text: 'Shoprenter', value: 'shoprenter' },
          { text: 'Unas Platform', value: 'unasPlatform' },
          { text: 'Woocommerce Platform', value: 'woocommercePlatform' },
          { text: 'Magento Platform', value: 'magentoPlatform' },
          { text: 'Gomag Platform', value: 'gomagPlatform' },
          { text: 'Romanian Domain', value: 'romanianDomain' },
          // { text: 'Full Service', value: 'fullService' },
          { text: 'MI Club', value: 'mi' },
        ],
        showError: false,
        exportLoading: false,
        region: 'all',
        displayInterval: 'arr',
        currency: 'usd',
        usdHufValue: 0,
        stats: {},
        labels: [],
        headers: [],
      };
    },
    computed: {
      capitalizedAccountType() {
        const type = this.selectedAccountType;
        return type.charAt(0).toUpperCase() + type.slice(1);
      },
      isMidmarket() {
        return this.selectedAccountType === 'midmarket';
      },
      mrrAttr() {
        return `${this.capitalizedAccountType}Mrr`;
      },
      activeAccountAttr() {
        return `toActiveAccount${this.mrrAttr}`;
      },
      isLoading() {
        return this.stats.mrr == null;
      },
      diffs() {
        const fromIndex = this.stats.diffs.length - this.numberOfDiffs;
        return this.stats.diffs.slice(fromIndex);
      },
      chartDimensions() {
        const displayInterval = this.displayInterval;
        const displayIntervalUpper = displayInterval.toUpperCase();
        const chartDimensions = [
          { text: 'Users', value: 'user' },
          { text: displayIntervalUpper, value: displayInterval },
          { text: 'New', value: 'new' },
          { text: 'Reactivate', value: 'reactivate' },
          { text: 'Upgrade', value: 'upgrade' },
          { text: 'Downgrade', value: 'downgrade' },
          { text: 'Cancel', value: 'cancel' },
          { text: 'Net Loss', value: 'net_loss' },
          { text: 'Net Loss %', value: 'net_loss_percent' },
          { text: 'Growth', value: 'growth' },
          { text: 'Churn', value: 'churn' },
          { text: 'Revenue Churn', value: 'mrr_churn' },
          { text: 'ARPU', value: 'arpu' },
          { text: 'LTV', value: 'ltv' },
          { text: `Lifetime (${this.groupBy})`, value: 'lifetime' },
          { text: 'Leads', value: 'lead' },
          { text: 'Lead To Pay', value: 'leadToPay' },
        ];

        return chartDimensions;
      },
      getActiveMerchantCount() {
        const free = this.getUserAllActiveFreeCount();
        const paying = this.stats.impressionStats.paying.all;
        return this.formatNumber(free + paying, false);
      },
    },
    watch: {
      currency() {
        this.renderChart();
      },
      displayInterval() {
        this.renderChart();
      },
      groupBy() {
        this.fetchStats();
      },
      numberOfDiffs() {
        this.renderChart();
      },
      region() {
        this.renderChart();
      },
      selectedChartDimension() {
        this.renderChart();
      },
      selectedAccountType() {
        this.renderChart();
      },
    },
    mounted() {
      this.fetchStats();
    },
    methods: {
      async fetchStats() {
        try {
          this.stats.mrr = null;
          const { data: stats } = await this.$axios.get(`/stats/all`, {
            params: {
              groupBy: this.groupBy,
            },
          });
          this.stats = stats;
          this.usdHufValue = stats.usd;
          this.$nextTick(() => {
            this.renderChart();
          });
        } catch (e) {
          console.log('Fetch data error', e.message);
        }
      },
      updateHeadersAndLabels() {
        this.labels = this.diffs.map((diff) => {
          let formattedDate = '';
          if (this.groupBy === 'week') {
            formattedDate = shortFormatDay(diff.from);
          } else {
            formattedDate = shortFormatMonth(diff.to);
          }
          return `${formattedDate} (${diff.exchangeRate})`;
        });

        this.headers = this.labels.map((e) => {
          return {
            text: e,
            align: 'center',
            sortable: false,
            value: e,
          };
        });
        this.headers.unshift({
          text: '',
          align: 'center',
          sortable: false,
          value: '',
        });
      },
      renderChart() {
        this.updateHeadersAndLabels();
        this.values = this.diffs.map((e, i) => {
          let value;
          let attr;
          switch (this.selectedChartDimension) {
            case 'user':
              value = this.getUserCount(e);
              break;
            case 'lead':
              value = this.getLeadCount(e);
              break;
            case 'mrr':
              value = this.changeCurr(e[this.activeAccountAttr], e);
              break;
            case 'arr':
              value = this.changeCurr(e[this.activeAccountAttr], e);
              break;
            case 'growth':
              if (i > 0) {
                value = this.getGrowthValue(e, this.diffs[i - 1]).value;
              } else {
                value = 0;
              }
              break;
            case 'churn':
              value = this.getChurnValue(e);
              break;
            case 'mrr_churn':
              value = this.getMrrChurnValue(e);
              break;
            case 'net_loss':
              value = this.getNetLossValue(e);
              break;
            case 'net_loss_percent':
              value = this.getNetLossPercent(e);
              break;
            case 'arpu':
              value = this.getArpuValue(e);
              break;
            case 'ltv':
              value = this.getLtvValue(e);
              break;
            case 'lifetime':
              value = this.getLifetimeValue(e);
              break;
            case 'leadToPay':
              value = e.newAll[this.region] / e.leadCount[this.region];
              break;
            default:
              attr = `${this.selectedChartDimension + this.capitalizedAccountType}Mrr`;
              value = this.changeCurr(e[attr], e);
              break;
          }
          return value;
        });
        if (window.dashboardChart) {
          window.dashboardChart.destroy();
        }
        const ctx = window.document.getElementById('mrrChart').getContext('2d');
        /* eslint-disable no-new */
        const SKIP_CURRENCY = ['user', 'lead', 'lifetime'];
        const USE_PERCENTAGE = ['churn', 'mrr_churn', 'leadToPay', 'net_loss_percent'];

        window.dashboardChart = new window.Chart(ctx, {
          type: 'bar',
          data: {
            labels: this.labels,
            datasets: [
              {
                backgroundColor: '#6078FF',
                label: 'MRR',
                data: this.values,
              },
            ],
          },
          options: {
            maintainAspectRatio: false,
            legend: {
              display: false,
            },
            tooltips: {
              callbacks: {
                label: (tooltipItem, data) => {
                  if (USE_PERCENTAGE.includes(this.selectedChartDimension)) {
                    return formatPercentage(tooltipItem.yLabel);
                  }
                  if (SKIP_CURRENCY.includes(this.selectedChartDimension)) {
                    return this.formatNumber(tooltipItem.yLabel, false);
                  }
                  return this.formatNumber(tooltipItem.yLabel);
                },
              },
            },
            scales: {
              yAxes: [
                {
                  ticks: {
                    callback: (value, index, values) => {
                      if (USE_PERCENTAGE.includes(this.selectedChartDimension)) {
                        return formatPercentage(value);
                      }
                      if (SKIP_CURRENCY.includes(this.selectedChartDimension)) {
                        return this.formatNumber(value, false);
                      }
                      return this.formatNumber(value);
                    },
                    // beginAtZero: true
                    maxTicksLimit: 6,
                  },
                  offsetGridLines: true,
                },
              ],
              xAxes: [
                {
                  gridLines: {
                    display: false,
                  },
                },
              ],
            },
          },
        });
      },
      getUserActivity(type) {
        if (type === 'free') {
          const freeActive = this.stats.impressionStats.free.all;
          const { freeCount30DaysAgo } = this.stats;
          const percentage = formatPercentage(freeActive / freeCount30DaysAgo);
          return `${percentage} (${freeActive})`;
        }
        const payingCount = this.stats.stats.all.count;
        const payingActive = this.stats.impressionStats.paying.all;
        const percentage = formatPercentage(payingActive / payingCount);
        return `${percentage} (${payingActive})`;
      },
      getUserAllActiveFreeCount() {
        const freeActive = this.stats.impressionStatsAllTime.free.all;
        return freeActive;
      },
      getLeadCount(diff) {
        return diff.leadCount[this.region];
      },
      getUserCount(diff) {
        const attr = `toActiveAccount${this.capitalizedAccountType}`;
        return diff[attr][this.region];
      },
      getGrowthValue(current, prev) {
        const prevMrr = prev[this.activeAccountAttr];
        const currentMrr = current[this.activeAccountAttr];
        const mrrDiff = {
          com: currentMrr.com - prevMrr.com,
          hu: currentMrr.hu - prevMrr.hu,
        };

        const value = this.changeCurr(mrrDiff, current);
        const count = this.getUserCount(current) - this.getUserCount(prev);

        return { value, count };
      },
      getChurnValue(diff) {
        const cancelAttr = `cancel${this.capitalizedAccountType}`;
        const activeCount = this.getUserCount(diff);
        return diff[cancelAttr][this.region] / activeCount;
      },
      getMrrChurnValue(diff) {
        const currentMrr = this.changeCurr(diff[this.activeAccountAttr], diff);
        const cancelMrr =
          this.changeCurr(diff[`cancel${this.mrrAttr}`], diff) +
          this.changeCurr(diff[`downgrade${this.mrrAttr}`], diff);
        return cancelMrr / currentMrr;
      },
      getNetLossValue(diff) {
        const reactivate = diff[`reactivate${this.mrrAttr}`];
        const upgrade = diff[`upgrade${this.mrrAttr}`];
        const downgrade = diff[`downgrade${this.mrrAttr}`];
        const cancel = diff[`cancel${this.mrrAttr}`];
        const netLossObj = {
          com: reactivate.com + upgrade.com - downgrade.com - cancel.com,
          hu: reactivate.hu + upgrade.hu - downgrade.hu - cancel.hu,
        };
        return this.changeCurr(netLossObj, diff);
      },
      getNetLossPercent(diff) {
        const mrrValue = this.changeCurr(diff[`toActiveAccount${this.mrrAttr}`], diff);
        const netLoss = this.getNetLossValue(diff);
        return netLoss / mrrValue;
      },
      getArpuValue(diff) {
        const currentMrr = this.changeCurr(diff[this.activeAccountAttr], diff);
        return currentMrr / this.getUserCount(diff);
      },
      getLtvValue(diff) {
        return this.getArpuValue(diff) / this.getChurnValue(diff);
      },
      getLifetimeValue(diff) {
        return 1 / this.getChurnValue(diff);
      },
      async exportPayingAccount() {
        this.exportLoading = true;
        try {
          const { data } = await this.$axios.get('/stats/export', {
            responseType: 'blob',
          });
          this.exportLoading = false;
          saveAs(data, `accounts_export.csv`);
        } catch (e) {
          this.exportLoading = false;
          this.showError = true;
        }
      },
      selectDimension(type) {
        this.selectedChartDimension = type;
      },
      openAccountModal(type, index) {
        if (NO_DIALOG_ROWS.includes(type)) {
          return;
        }
        const diff = this.diffs[index - 1];
        this.accountMrrDialogSettings.show = true;
        this.accountMrrDialogSettings.type = type;
        this.accountMrrDialogSettings.diff = diff;
        this.accountMrrDialogSettings.displayInterval = this.displayInterval;
        this.accountMrrDialogSettings.groupBy = this.groupBy;
        this.accountMrrDialogSettings.segment = this.capitalizedAccountType;
        this.accountMrrDialogSettings.currency = this.currency;
        this.accountMrrDialogSettings.region = this.region;
        this.accountMrrDialogSettings.usdHufValue = this.usdHufValue;
      },
      getTableValue(type, index) {
        const diff = this.diffs[index - 1];
        if (type === 'user') {
          return this.getUserCount(diff);
        }
        if (type === 'lead') {
          return this.getLeadCount(diff);
        }
        if (type === 'mrr' || type === 'arr') {
          const mrrObj = this.changeCurr(diff[`toActiveAccount${this.mrrAttr}`], diff);
          return this.formatNumber(mrrObj);
        }
        if (type === 'growth') {
          if (index > 1) {
            const { value, count } = this.getGrowthValue(diff, this.diffs[index - 2]);
            return `${this.formatNumber(value)} (${count})`;
          }
          return '';
        }
        if (type === 'churn') {
          const value = this.getChurnValue(diff);
          return formatPercentage(value);
        }
        if (type === 'mrr_churn') {
          const value = this.getMrrChurnValue(diff);
          return formatPercentage(value);
        }
        if (type === 'net_loss') {
          const netLoss = this.getNetLossValue(diff);
          return this.formatNumber(netLoss);
        }
        if (type === 'net_loss_percent') {
          const netLoss = this.getNetLossPercent(diff);
          return formatPercentage(netLoss);
        }
        if (type === 'arpu') {
          const value = this.getArpuValue(diff);
          return this.formatNumber(value);
        }
        if (type === 'ltv') {
          const value = this.getLtvValue(diff);
          return this.formatNumber(value);
        }
        if (type === 'lifetime') {
          let value = this.getLifetimeValue(diff);
          value = value.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          });
          return value;
        }
        if (type === 'leadToPay') {
          const value = diff.newAll[this.region] / diff.leadCount[this.region];
          return formatPercentage(value);
        }
        const mrrObj = this.changeCurr(diff[type + this.mrrAttr], diff);
        const countAttr = type + this.capitalizedAccountType;
        const count = diff[countAttr][this.region];
        return `${this.formatNumber(mrrObj)} (${count})`;
      },
      changeCurr({ com, hu }, diff) {
        const { currency, region, usdHufValue } = this;
        if (this.displayInterval === 'arr') {
          com *= 12;
          hu *= 12;
        }
        const exchangeRate = diff?.exchangeRate || usdHufValue;
        return changeCurr({ com, hu }, region, currency, exchangeRate);
      },
      formatNumber(value, addCurrency = true) {
        const { currency } = this;
        return formatNumber(value, currency, addCurrency);
      },
      getTopCardValue(type, region) {
        let result = '-Unknown-';
        const diff = this.diffs[this.diffs.length - 1];
        if (type === 'freeUsers') {
          result = this.formatNumber(this.stats.freeCount, false);
        }
        if (type === 'payingUsers') {
          switch (region) {
            case 'all':
              result = this.formatNumber(this.stats.stats.all.count, false);
              break;
            case 'com':
              result = this.formatNumber(this.stats.stats.international.count, false);
              break;
            case 'hu':
              result = this.formatNumber(this.stats.stats.hungary.count, false);
              break;
          }
        }

        const isMrr = type.toLowerCase().includes('mrr');
        const isArr = type.toLowerCase().includes('arr');
        if (isMrr || isArr) {
          const isMidmarket = type.includes('midmarket');
          const mrrAttr = isMidmarket ? 'MidmarketMrr' : 'AllMrr';
          const multiplier = isArr ? 12 : 1;
          const mrrObj = _clone(diff[`toActiveAccount${mrrAttr}`]);
          if (region === 'hu') mrrObj.com = 0;
          if (region === 'com') mrrObj.hu = 0;

          const { currency, usdHufValue } = this;
          const val = changeCurr(mrrObj, region, currency, usdHufValue) * multiplier;
          result = this.formatNumber(val);
        }

        if (type === 'activity') {
          switch (region) {
            case 'all':
              result = formatPercentage(0.8046);
              break;
            case 'com':
              result = formatPercentage(0.7967);
              break;
            case 'hu':
              result = formatPercentage(0.8161);
              break;
          }
        }

        return result;
      },
      getTdStyle(type) {
        const result = {};
        if (!NO_DIALOG_ROWS.includes(type)) {
          result.cursor = 'pointer';
        } else {
          result.cursor = 'default';
        }
        return result;
      },
      getRowStyle(type) {
        const style = {};
        if (type === this.selectedChartDimension) {
          style['background-color'] = 'rgba(96, 120, 255, 0.1)';
        }
        return style;
      },
      getCellStyle(type) {
        const result = {
          'font-weight': 500,
        };
        if (['new', 'reactivate', 'upgrade'].includes(type)) {
          result.color = '#12C457';
        }
        if (['downgrade', 'cancel'].includes(type)) {
          result.color = '#E84C85';
        }
        return result;
      },
    },
  };
</script>

<style lang="sass" scoped>
  .capitalize
    text-transform: capitalize
  .align-end
    display: flex
    align-items: center
    justify-content: flex-end
  .dash-blue
    color: #6078FF
</style>

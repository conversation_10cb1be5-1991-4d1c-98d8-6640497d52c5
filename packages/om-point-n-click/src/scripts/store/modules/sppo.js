import { toast } from 'vue3-toastify';
import apiService from '../../services/api';
import { getProductId, getPageIdentifier } from '../../helper';
import TYPES from '../../services/actions/helper/types';
import SmartProductTag from '../../services/actions/SmartProductTag';

const _fetchSmartProductTags = async (databaseId, productId) => {
  try {
    const variables = await apiService.getSmartProductTags(databaseId, productId);
    return variables;
  } catch (error) {
    console.error('Error while fetching generated smart product tags:', error);
    return false;
  }
};

const _generateSmartProductTags = async (databaseId, productId, promptIds) => {
  try {
    const response = await apiService.generateSmartProductTags(databaseId, productId, promptIds);
    if (response.status >= 200 && response.status < 400) {
      return response.body;
    }

    const hasErrorMessage = response.body?.errors?.[0]?.message;
    if (hasErrorMessage) {
      console.error(response);
      toast(hasErrorMessage, {
        type: 'error',
        theme: 'light',
        position: 'bottom-right',
      });
    }

    return false;
  } catch (error) {
    console.error('Error during smart product tag generation:', error);
    return false;
  }
};

const _fetchSmartPageTags = async (databaseId, slug) => {
  try {
    return await apiService.getSmartPageTags(databaseId, slug);
  } catch (e) {
    console.error('Error while fetching smart page tags', slug);
    return false;
  }
};

export const sppoModule = {
  namespaced: true,
  state: () => ({
    userPrompts: [],
    productTags: null,
    pageTags: null,
  }),
  getters: {
    smartProductTags({ userPrompts, productTags }) {
      if (!userPrompts || !productTags) return {};

      const result = {};
      userPrompts.forEach((prompt) => {
        const value = productTags[prompt.variableName];
        if (!value) return;

        result[prompt.variableName] = value;
      });

      return result;
    },
    smartPageTags({ pageTags }) {
      return pageTags;
    },
    getProductId() {
      return getProductId();
    },
  },
  mutations: {
    SET_USER_PROMPTS(state, data) {
      state.userPrompts = data;
    },
    SET_PRODUCTS_TAGS(state, data) {
      state.productTags = data;
    },
    SET_PAGE_TAGS(state, data) {
      state.pageTags = data;
    },
  },

  actions: {
    async fetchPPOData({ getters, dispatch, rootState }) {
      const isPPO = (change) => change.type === TYPES.SMART_PRODUCT_TAG;
      const changes = rootState.dynamicContent.changes;
      const hasPPO = changes.filter(isPPO);
      if (!hasPPO.length) return;

      try {
        await dispatch('fetchUserPrompts');
        await dispatch('fetchProductData');
      } catch (e) {
        console.error('Error while fetch Smart product page optimizer', e);
      }

      try {
        await dispatch('fetchSmartPageTags');
      } catch (e) {
        console.error('Error while fetch Smart page tags', e);
      }

      hasPPO.forEach((change) => {
        const value =
          getters.smartProductTags[change.settingVariable] ||
          getters.smartPageTags[change.settingVariable];
        if (!value) return;

        dispatch(
          'dynamicContent/modifyChangeById',
          {
            id: change.id,
            property: 'content',
            to: value,
          },
          { root: true },
        );
        SmartProductTag.applyChange({ change });
      });
    },

    async fetchUserPrompts({ state, commit, rootState }) {
      if (state.userPrompts.length) return state.userPrompts;

      try {
        const response = await apiService.getUserPrompts(rootState.databaseId);
        if (!response.length) throw new Error('there is no user prompts');

        commit('SET_USER_PROMPTS', response);
      } catch (error) {
        console.error('Error while fetching user prompts:', error);
      }
    },
    getPromptsWithoutProductTag({ state }, productTags) {
      return state.userPrompts.filter((prompt) => !productTags?.[prompt.variableName]);
    },
    async fetchProductData({ state, commit, dispatch, getters, rootState }) {
      if (state.productTags) return state.productTags;

      const databaseId = rootState.databaseId;
      const productId = getters.getProductId;

      let productTags = await _fetchSmartProductTags(databaseId, productId);
      const promptsWithoutProductTag = await dispatch('getPromptsWithoutProductTag', productTags);

      if (!productTags || promptsWithoutProductTag.length) {
        const promptIds = promptsWithoutProductTag.map(({ _id }) => _id);
        const success = await _generateSmartProductTags(databaseId, productId, promptIds);
        if (success) {
          productTags = await _fetchSmartProductTags(databaseId, productId);
        }
      }

      if (productTags) {
        commit('SET_PRODUCTS_TAGS', productTags);
      }
    },

    async fetchSmartPageTags({ commit, rootState }) {
      const databaseId = rootState.databaseId;
      const slug = await getPageIdentifier();
      const tags = await _fetchSmartPageTags(databaseId, slug);

      if (tags) {
        commit('SET_PAGE_TAGS', tags);
      }
    },
  },
};

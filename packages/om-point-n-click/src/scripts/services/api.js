import { AUTH_HEADER_NAME } from '../constants';
import { getSettings, purifyDomain } from '../helper';

class ApiService {
  constructor() {
    this.token = '';
    this.abortController = new AbortController();
  }

  getToken() {
    return this.token;
  }

  setToken(token) {
    this.token = token;
  }

  async fetchWithAbort(url, options) {
    return fetch(url, {
      ...options,
      signal: this.abortController.signal,
    });
  }

  abortCurrentRequest() {
    this.abortController.abort();
    this.abortController = new AbortController();
  }

  async getPositions() {
    let result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/positions`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });
    const ok = result.status >= 200 && result.status < 300;
    if (ok) {
      result = await result.json();
      if (result.deleted) {
        console.log('[web-selector] campaign not found');
        throw new Error('campaign_not_found');
      }
    } else {
      result = { positions: [] };
      console.log('[web-selector] get positions failed');
    }

    return result;
  }

  async getPreviewUrl() {
    const result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/previewUrl`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });

    return result.json();
  }

  updatePositions(positions) {
    return fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/positions`, {
      method: 'POST',
      body: JSON.stringify({ positions }),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });
  }

  async getChanges() {
    let result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/changes`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });

    const ok = result.status >= 200 && result.status < 300;

    if (!ok) throw new Error('unable to load changes');

    result = await result.json();

    return result;
  }

  async getCampaign() {
    let result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/campaign`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });

    const ok = result.status >= 200 && result.status < 300;

    if (!ok) throw new Error('unable to load campaign rules');

    result = await result.json();

    return result;
  }

  updateChanges({ changes, page, isNew, featureMode }) {
    const settings = getSettings();

    return fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/changes`, {
      method: 'POST',
      body: JSON.stringify({ changes, page, isNew, domain: settings.domain, featureMode }),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });
  }

  async getUserDataById() {
    let result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/user-information`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });

    result = await result.json();

    return result;
  }

  async getDefaultPrompts() {
    const settings = getSettings();
    let result = await fetch(
      `${window.OptiMonkRegistry.beUrl}/api/web-selector/default-prompts?domain=${settings.domain}`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          [AUTH_HEADER_NAME]: this.token,
        },
      },
    );

    if (result.status !== 200) {
      return {};
    }

    result = await result.json();

    return result.prompts;
  }

  updateProductTourStatus(type, status) {
    return fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/product-tour-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
      body: JSON.stringify({
        type,
        status,
      }),
    });
  }

  async generateSABVersions(prompt, versions, originalContent) {
    const settings = getSettings();
    let result = { success: true, result: [] };

    try {
      result = await this.fetchWithAbort(
        `${window.OptiMonkRegistry.beUrl}/api/web-selector/generate-sab-versions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            [AUTH_HEADER_NAME]: this.token,
          },
          body: JSON.stringify({ prompt, versions, domain: settings.domain, originalContent }),
        },
      );

      result = await result.json();
    } catch (e) {
      if (e.name === 'AbortError') {
        return { success: false, result: 'cancelled' };
      }

      console.error('Error while generating SAB Versions', { errorMessage: e.message });
      return { success: false, result: 'unknown_error' };
    }

    return result;
  }

  async getUserPrompts() {
    const settings = getSettings();
    try {
      let result = await fetch(
        `${window.OptiMonkRegistry.beUrl}/api/web-selector/user-prompts?domain=${settings.domain}`,
        {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            [AUTH_HEADER_NAME]: this.token,
          },
        },
      );

      result = await result.json();

      return result;
    } catch (e) {
      return [];
    }
  }

  async getSmartProductTags(databaseId, productId) {
    const { shopId } = window.OptiMonkEmbedded.Engine.getInfo();
    if (!shopId) throw Error('[SPPO] No shop id found');

    try {
      let result = await fetch(
        `${
          window.OptiMonkRegistry.aiPPOCdnUrl
        }/${databaseId}/${shopId}/${productId}.json?v=${new Date().getTime()}`,
        {
          method: 'GET',
          headers: {
            Accept: 'application/json',
          },
        },
      );

      result = await result.json();

      return result;
    } catch (e) {
      return null;
    }
  }

  async generateSmartProductTags(databaseId, productId, promptIds) {
    const result = await fetch(
      `${window.OptiMonkRegistry.beUrl}/api/web-selector/generate-ppo-variables`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          [AUTH_HEADER_NAME]: this.token,
        },
        body: JSON.stringify({
          databaseId,
          productId,
          promptIds,
        }),
      },
    );

    return { status: result.status, body: await result.json() };
  }

  createPreview(changes) {
    const settings = getSettings();

    return fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/preview`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
      body: JSON.stringify({ changes, domain: settings.domain }),
    });
  }

  async getSPPrompts() {
    let result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/sp/prompts`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        [AUTH_HEADER_NAME]: this.token,
      },
    });

    result = await result.json();

    return result;
  }

  async getSmartPageTags(databaseId, slug) {
    let { shopId } = window.OptiMonkEmbedded.Engine.getInfo();
    shopId = shopId || purifyDomain(window.location.host);

    if (!shopId) throw Error('[SPPO] No shop id found');
    let result = await fetch(
      `${window.OptiMonkRegistry.aiPPOCdnUrl}/${databaseId}/${shopId}/${slug}.json`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      },
    );

    result = await result.json();

    return result;
  }

  async getTopKeywords() {
    const settings = getSettings();
    let result = await fetch(
      `${window.OptiMonkRegistry.beUrl}/api/web-selector/sp/top-keywords?domain=${settings.domain}`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          [AUTH_HEADER_NAME]: this.token,
        },
      },
    );

    result = await result.json();

    return result;
  }

  async getDomainContext() {
    const settings = getSettings();
    let result = await fetch(
      `${window.OptiMonkRegistry.beUrl}/api/web-selector/sp/domain-context?domain=${settings.domain}`,
      {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          [AUTH_HEADER_NAME]: this.token,
        },
      },
    );

    result = await result.json();

    return result;
  }

  async generateSP(prompt, topKeywords) {
    let result = { success: true, result: [] };
    try {
      result = await fetch(`${window.OptiMonkRegistry.beUrl}/api/web-selector/sp/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          [AUTH_HEADER_NAME]: this.token,
        },
        body: JSON.stringify({
          prompt,
          topKeywords,
        }),
      });

      result = await result.json();
    } catch (e) {
      console.error('Error while generating SP', { errorMessage: e.message });
      return { success: false, result: 'unknown_error' };
    }

    return result;
  }
}

export default new ApiService();

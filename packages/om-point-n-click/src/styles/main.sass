$om-orange: #ED5A29
$navbar-height: 49px

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap')

[class^="om-"]
  box-sizing: border-box

:root
  --toastify-z-index: 9999999999999
// Remove contenteditable default border
[contenteditable]
  outline: 0px solid transparent
[contenteditable]:focus
  box-shadow: none !important

.om-overlay
  display: block !important
  position: absolute
  background-color: rgba(0, 0, 0, 0.6)
  z-index: 9999
  width: 100%
  &.om-top
    top: 0
  &.om-bottom
    height: 100%
html
  scroll-behavior: smooth
#om-web-selector
  font-family: 'Roboto', sans-serif !important
  font-size: 14px !important
  font-weight: 400

  svg
    width: unset !important
    height: unset !important

  .om-separator
    min-width: 1px
    height: 100%
    display: block !important
    background: #828282
    margin-right: 12px
    margin-left: 12px
  .text-overflow
    overflow: hidden
    text-overflow: ellipsis
    white-space: nowrap
    display: inline-block !important
  .om-disabled
    opacity: 0.3
    pointer-events: none
  .btn
    padding: 3px 25px
    font-size: 14px
    display: flex
    justify-content: center
    align-items: center
    text-transform: unset
    cursor: pointer
    font-family: 'Roboto', sans-serif !important
    font-weight: 500
  .om-web-selector-navbar
    visibility: visible
    position: fixed !important
    top: 0 !important
    left: 0 !important
    right: 0 !important
    width: 100% !important
    background-color: #333036 !important
    border-bottom: 1px solid #111 !important
    z-index: 2147483647 !important
    height: $navbar-height
    color: white
    display: flex
    flex-direction: row
    padding: 8px
    .dynamic-content, .embedded-placement, .om-preview-mode
      width: 100%
      padding-right: 8px
      .slide-enter-active,
      .slide-leave-active
        transition: all 0.15s ease-out

      .slide-enter-from
        opacity: 0
        transform: translateY(30px)

      .slide-leave-to
        opacity: 0
        transform: translateY(-30px)
      .om-top-bar
        display: flex

        .om-back-area
          cursor: pointer
          display: flex
          flex: 1
          justify-content: flex-start
          align-items: center
          overflow: hidden
          .om-top-bar-back-button
            vertical-align: middle

        .embedded-position
          font-size: 12px
          line-height: 16px
          .selected-page
            margin-right: 10px
            text-decoration: none
            color: $om-orange
        .om-center-area
          display: flex
          flex: 0 0 auto
          align-items: center
          justify-content: flex-end
          .om-preview
            margin-right: 7px
            color: white
          .om-preview-tooltip
            display: flex
            flex-direction: column
            justify-content: center
            min-height: 100px
            min-width: 135px
            gap: 10px
            &.om-tooltip-wider
              min-width: 205px
            .om-button
              color: #ffffff
          .om-history
            display: flex
            align-items: center
            justify-content: center
            margin: 0 12px 0 12px
            .om-history-action
              display: flex
              align-items: center
              cursor: pointer
              &.om-disabled
                cursor: not-allowed
                pointer-events: none
              svg
                &:first-child
                  margin-right: 4px
                &:last-child
                  margin-left: 4px
          .changes
            margin-left: auto
            margin-right: 12px
            font-weight: 400
            background: rgba(185, 190, 198, 0.2)
            border-radius: 4px
            height: 32px
          .changes-warning
            margin-left: 5px
            .om-tooltip
              display: flex
              align-self: center
          .om-switch-wrapper
            display: flex
            .om-tooltip
              display: flex
              align-items: center
            .om-switch-label
              display: flex
              align-items: center
              margin-right: 8px
              font-weight: 300
          .om-mode-selector
            cursor: pointer
            display: flex
            border-radius: 6px
            height: 32px
            border: 1px solid #4F4C52
            overflow: hidden
            font-weight: 300
            &-btn
              display: flex
              justify-content: center
              align-items: center
              width: 90px
              font-size: 14px
              font-weight: 500
              text-transform: unset
              &.om-active
                background: #FFFFFF
                color: #333036
                transition: all 0.25s linear
              &.om-disabled
                opacity: 0.2
                cursor: not-allowed
                pointer-events: none
        .marker-area
          display: flex
          align-items: center
        .action-area
          display: flex
          justify-content: center
          align-items: center
          width: 10%
          min-width: 100px
          .btn
            background: $om-orange
            cursor: pointer
            padding: 3px 25px
            font-size: 13px
            border-radius: 6px
            height: 32px
            display: flex
            justify-content: center
            align-items: center
            text-transform: unset
  .om-m-0
    margin: 0
.om-web-selector-outline
  outline: 2px solid $om-orange !important
  outline-offset: 2px !important
.om-web-selector-outline-hover
  outline: 2px dotted $om-orange !important
  outline-offset: 2px !important
  transition: outline 0.15s !important
.om-web-selector-placeholder
  width: 100%
  margin-top: 10px
  margin-left: -4px
  position: relative
  border: 2px dashed $om-orange
  display: flex
  .om-holder
    flex: 0 0 100%
.om-position-popup
  font-family: 'Roboto', sans-serif !important
  position: absolute
  width: 210px
  height: 210px
  border-radius: 6px
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2)
  z-index: 999
  background-color: #FFFFFF
  .position-popup-header
    height: 45px
    border-bottom: 1px solid #EEEFF1
    .position-popup-title
      position: absolute
      left: 9.95%
      right: 36.06%
      top: 6.36%
      bottom: 85.45%
      font-style: normal
      font-weight: normal
      font-size: 15px
      line-height: 0
      display: flex
      align-items: center
      text-transform: uppercase
      color: #828282
    .position-popup-close
      position: absolute
      width: 10px
      height: 10px
      left: 186px
      top: 11px
  .position-popup-body
    width: 100%
    height: 100%
    .position-popup-option
      cursor: pointer
      position: absolute
      height: 54px
      width: 78px
      top: 69px
      box-sizing: border-box
      &.above
        left: 21px
      &.below
        left: 110px
    .position-popup-label
      position: absolute
      height: 16px
      width: 78px
      top: 127px
      font-style: normal
      font-weight: normal
      font-size: 12px
      line-height: 14px
      text-align: center
      color: #272727
      &.above
        left: 21px
      &.below
        left: 110px
      &.selected
        font-weight: bold
    .position-popup-confirm
      position: absolute
      height: 30px
      width: 91px
      left: 59px
      top: 161px
      border-radius: 3px
      background: $om-orange
      border: 1px solid $om-orange
      box-shadow: 0 3px 10px rgba(237, 90, 41, 0.15)
      font-style: normal
      font-weight: 500
      font-size: 14px
      color: #FFFFFF
      padding: 0
      margin: 0

.om
  &-mb
    &-6
      margin-bottom: 6px

.gap-sm
  gap: 10px

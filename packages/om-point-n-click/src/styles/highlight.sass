.om-web-selector-outline-show
  position: relative
  outline: 5px solid #8444E1
  outline-offset: 2px
  transition: outline 0.15s
  border-radius: 0 0 4px 4px
  min-width: 200px
  &:hover::before
    border-top-left-radius: 4px
    border-top-right-radius: 4px
    display: flex
    justify-content: center
    align-items: center
    font-family: 'Roboto', sans-serif
    position: absolute
    font-size: 14px
    color: #FFFFFF
    background: #8444E1
    font-weight: 500
    width: 137px
    padding: 5px
    height: 32px
    top: -35px
    left: -7px
    box-sizing: border-box
    text-transform: capitalize
    z-index: 99999
  &[data-om-change-type="text"]
    &:hover::before
      content: 'Edit text'
  &[data-om-change-type="html"]
    &:hover::before
      content: 'Edit HTML'
  &[data-om-change-type="appearance"]
    &:hover::before
      content: 'Appearance'
  &[data-om-change-type="insert-text"]
    &:hover::before
      content: 'Insert text'
  &[data-om-change-type="insert-html"]
    &:hover::before
      content: 'Insert HTML'
  &[data-om-change-type="smart-ab-test"]
    &:hover::before
      content: 'Smart A/B test'
  &[sab-current-index]
    &:hover::before
      content: 'Smart A/B test (' attr(sab-current-index) ')'
      width: 145px
  &[data-om-change-type="smart-product-tag"]
    &:hover::before
      content: 'Smart product tag'
  &[data-om-change-type="smart-personalization"]
    &:hover::before
      content: 'Smart element'
      width: 165px
  &[data-om-change-type="edit-style"]
    &:hover::before
      content: 'Edit style'

.om-element-hidden
  position: relative
  &::after
    position: absolute !important
    content: ''
    height: 50px
    width: 50px
    top: calc(50% - 25px) !important
    left: calc(50% - 25px) !important
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='rgb(237, 90, 41)' class='bi bi-eye-slash' viewBox='0 0 16 16'%3E%3Cpath d='M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z'/%3E%3Cpath d='M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z'/%3E%3Cpath d='M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z'/%3E%3C/svg%3E")
    background-repeat: no-repeat
    background-size: 50px 50px
    z-index: 999 !important

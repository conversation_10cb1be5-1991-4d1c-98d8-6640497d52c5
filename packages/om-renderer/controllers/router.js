import express from 'express';
import mongoose from 'mongoose';
import { createRequire } from 'module';
import { merge } from 'lodash-es';
import {
  replaceImages,
  logoTypeWhitelist,
  getNameFromURL,
} from '@om/template-properties/src/imageReplace.js';
import { replaceTranslations } from '@om/template-properties/src/translate/translate.js';
import { logoHide } from '@om/template-properties/src/logoHide.js';
import * as imageUtil from '../src/utils/imageUtil.js';
import { isEmbedded } from '../src/utils/embedded.js';
import { render, renderPreview, renderThumbnail } from '../dist/render.js';
import logger from '../src/logger/logger.js';
import { getFonts } from '../services/fonts.js';
import { getTranslations } from '../services/translations.js';

const router = express.Router();
const require = createRequire(import.meta.url);
const baseFonts = require('../test/fonts.json');

const getPoweredBy = async (databaseId, locale) => {
  const accountCollection = mongoose.connection.db.collection('master_accounts');
  const account = await accountCollection.findOne(
    { databaseId: parseInt(databaseId, 10) },
    { type: 1, 'settings.hasPoweredByLinkDisabled': 1, 'billing.package': 1 },
  );
  const isPoweredByDisabledByPackage =
    account.type === 'normal' &&
    ['ESSENTIAL', 'GROWTH', 'PREMIUM', 'MASTER', 'DEMO'].some((e) =>
      account.billing.package.startsWith(e),
    );
  let isPoweredByEnabled = true;
  if (!isPoweredByDisabledByPackage) {
    if (account.type === 'sub') {
      const { settings } = await accountCollection.findOne(
        { subAccounts: { $in: [account._id] } },
        { 'settings.hasPoweredByLinkDisabled': 1 },
      );
      isPoweredByEnabled = settings.hasPoweredByLinkDisabled !== true;
    } else {
      isPoweredByEnabled = account.settings.hasPoweredByLinkDisabled !== true;
    }
  } else {
    isPoweredByEnabled = false;
  }

  return {
    link: `//landing.optimonk.${
      locale === 'hu' ? 'hu' : 'com'
    }/powered-by-optimonk/?utm_source=link&utm_medium=optimonk_popup`,
    text: 'Made with ♥️ by OptiMonk',
    visible: isPoweredByEnabled,
  };
};

const getAccountFeatures = async (databaseId) => {
  const accountCollection = mongoose.connection.db.collection('master_accounts');
  const account = await accountCollection.findOne(
    { databaseId: parseInt(databaseId, 10) },
    { features: 1 },
  );

  return account?.features || [];
};

const translate = async (template, language) => {
  if (language === 'en') return;

  try {
    const translations = await getTranslations(template._id, language);
    if (translations) {
      const { elements, wheelOptions } = replaceTranslations(template, translations);
      template.elements = elements;
      template.wheelOptions = wheelOptions;
    } else {
      logger.warn({
        message: `No translation found for template ${template._id} in ${language} language`,
      });
    }
  } catch (err) {
    logger.warn(
      {
        errorMessage: err.message,
        stack: err.stack,
      },
      '[SSR] Template translation error',
    );
    throw err;
  }
};

const renderResult = async ({
  template,
  databaseId,
  campaignId,
  variantId,
  fonts = baseFonts,
  poweredBy = {},
  onlyContent = false,
  color = null,
  templateName = '',
  locale = 'en',
  customTheme = null,
  features = [],
  type = '',
  isAdmin = false,
}) => {
  logger.info(`renderResult started , databaseId: ${databaseId}, variantId: ${variantId}`);
  const result = await render({
    template,
    fonts,
    poweredBy,
    databaseId,
    campaignId,
    variantId,
    templateName,
    locale,
    customTheme,
    features,
    type,
    isAdmin,
  });

  return renderPreview({ campaignId, html: result, onlyContent, color });
};

const renderTemplatePreview = async ({
  template,
  campaignId,
  variantId,
  fonts = baseFonts,
  poweredBy = {},
  onlyContent = false,
  color = null,
  templateName = '',
  locale = 'en',
  customTheme = null,
  features = [],
}) => {
  if (customTheme) {
    campaignId = `${campaignId}-${customTheme._id}`;
  }
  const result = await render({
    template,
    fonts,
    poweredBy,
    campaignId,
    variantId,
    templateName,
    locale,
    customTheme,
    features,
  });

  return renderThumbnail({ campaignId, html: result, onlyContent, color });
};

const thumbnailRender = async ({
  template,
  campaignId,
  databaseId,
  variantId,
  fonts = baseFonts,
  poweredBy = {},
  color = null,
  customTheme = null,
  features = [],
  type = '',
}) => {
  if (customTheme) {
    campaignId = `${campaignId}-${customTheme._id}`;
  }
  const result = await render({
    template,
    fonts,
    poweredBy,
    campaignId,
    variantId,
    databaseId,
    firstPage: true,
    customTheme,
    features,
    type,
    isAdmin: true,
  });

  const isEmbeddedType = isEmbedded(template);

  return renderThumbnail({ campaignId, html: result, color, isEmbeddedType, template });
};

const getMasterTemplate = async (templateId) => {
  const id = mongoose.Types.ObjectId(templateId);
  const db = mongoose.connection.db;
  const templatesCollection = `master_templates`;
  const { template, type, theme, displayName, _id } = await db
    .collection(templatesCollection)
    .findOne({ _id: id }, { template: 1, type: 1, theme: 1, displayName: 1 });

  template.type = type;
  template.theme = theme;
  template.displayName = displayName;
  template._id = _id;

  return template;
};

router.post('/', async (req, res) => {
  const { template, databaseId, campaignId, variantId, onlyContent } = req.body;
  try {
    const fonts = await getFonts(databaseId);
    let result = await renderResult({
      template,
      databaseId,
      campaignId,
      variantId,
      fonts,
      onlyContent,
    });
    result = imageUtil.replaceS3UrlWithCdn(result);

    return res.send(result);
  } catch (err) {
    logger.error(
      {
        errorMessage: err.message,
        databaseId,
        campaignId,
        variantId,
        stack: err.stack,
      },
      `[SSR] Error in getVariant by template`,
    );
    return res.status(500).send({ message: 'Unexpected error' });
  }
});

const getCustomTheme = async ({ name, account }) => {
  const db = mongoose.connection.db;
  const criteria = { databaseId: parseInt(account, 10) };

  if (/^[0-9a-fA-F]{24}$/.test(name)) {
    criteria._id = mongoose.Types.ObjectId(name);
  } else {
    criteria.name = name;
  }

  const customTheme = await db.collection('master_custom_themes').findOne(criteria);
  return customTheme;
};

const getSelectedImage = async ({ collection, name, databaseId }) => {
  if (!name) return null;

  const themeKitLogoHasTimestamp = /_\d{13}\./.test(name);

  const db = mongoose.connection.db;
  const image = await db.collection(collection).findOne({
    databaseId,
    url: themeKitLogoHasTimestamp
      ? { $regex: name }
      : new RegExp(
          `${name.replace(/\.((jpeg|jpg|jpeg-2000|png|svg|gif|tiff|webp))$/, '_\\d{13}.$1')}`,
        ),
  });
  return image;
};

const rendererContextMW = async (req, res, next) => {
  const { templateId } = req.params;
  const { color, account = 44, theme, language } = req.query;
  const { themeKit = null, baseTheme = false } = req.body;
  const { image } = themeKit ?? {};
  delete themeKit?.image;

  req.ctx = { account, render: { templateId, color, theme, themeKit }, language };
  try {
    const fonts = await getFonts(account);
    const template = await getMasterTemplate(templateId);
    req.ctx.render.fonts = fonts;
    req.ctx.render.template = template;
    const themeKitName = theme || template.themeKit?.id || themeKit?.id || template.themeKit?.name;
    req.ctx.render.themeKitName = themeKitName;
    let customTheme;
    let campaignId = templateId;
    if (themeKitName) {
      const themesAccountId = baseTheme ? 44 : account;
      customTheme = await getCustomTheme({ account: themesAccountId, name: themeKitName });
      if (customTheme) {
        campaignId = `${campaignId}-${customTheme?._id}`;
        customTheme.themeKit = merge(customTheme?.themeKit || {}, themeKit);
      }

      const ogLogoName = getNameFromURL(customTheme?.logo?.original);
      if (image?.url && ogLogoName && ogLogoName !== getNameFromURL(image?.name)) {
        req.ctx.render.template = replaceImages({
          template,
          imageData: {
            currentImage: customTheme.logo.original,
            selectedImage: { ...image, id: image?._id },
          },
          whitelist: logoTypeWhitelist,
        });
        req.ctx.render.template?.images?.push?.(image);
      }
    }
    req.ctx.campaignId = campaignId;
    req.ctx.render.customTheme = customTheme;
    next();
  } catch (e) {
    logger.error({
      errorMessage: e.message,
      stack: e.stack,
      ...req.params,
      ...req.query,
      ...req.body,
    });
    res.status(500).send({ message: 'Unexpected error' });
  }
};

router.post('/brand-kit-preview/:templateId', async (req, res) => {
  try {
    const { templateId } = req.params;
    const { color, account = 44, theme, language } = req.query;
    req.log.info('brandKitPreview started, databaseId: %d, templateId: %s', account, templateId);
    const fonts = await getFonts(account);
    let template = await getMasterTemplate(templateId);
    if (language) {
      await translate(template, language);
    }
    const themeKitName = theme || template.themeKit?.id || template.themeKit?.name;
    let customTheme;
    let campaignId = templateId;
    if (themeKitName) {
      customTheme = await getCustomTheme({ account, name: themeKitName });
      campaignId = `${campaignId}-${templateId}`;
    }

    const selectedImage = await getSelectedImage({
      collection: 'user_images',
      name: customTheme?.logo?.current,
      databaseId: Number(account),
    });

    const ogLogoName = getNameFromURL(customTheme?.logo?.original);
    const currentLogoName = getNameFromURL(customTheme?.logo?.current);

    if (ogLogoName && selectedImage && ogLogoName !== currentLogoName) {
      template = replaceImages({
        template,
        imageData: {
          templateName: template.displayName,
          currentImage: customTheme.logo.original,
          selectedImage: { ...selectedImage, id: selectedImage._id.toString() },
        },
      });
      template?.images?.push?.({
        _id: selectedImage._id.toString(),
        name: selectedImage.name,
        url: selectedImage.url,
        height: selectedImage.height,
        width: selectedImage.width,
        __typename: 'Image',
      });
    } else if (!selectedImage && !customTheme?.logo?.current && customTheme?.logo?.original) {
      logoHide(template.elements, customTheme.logo.original);
    }

    const result = await thumbnailRender({
      template,
      campaignId,
      fonts,
      color,
      customTheme,
    });
    return res.send(result);
  } catch (error) {
    logger.error({
      errorMessage: error.message,
      stack: error.stack,
      ...req.params,
      ...req.query,
      ...req.body,
    });

    if (error.message.includes('No translation')) {
      return res.status(400).send({ message: 'no_translation' });
    }

    return res.status(500).send({ message: 'Unexpected error' });
  }
});

router.post('/themekit-preview/:templateId', rendererContextMW, async (req, res) => {
  try {
    const { render = {}, campaignId, language } = req.ctx;
    const { template, fonts, color, customTheme } = render;
    if (language) {
      await translate(template, language);
    }
    const result = await thumbnailRender({
      template,
      campaignId,
      fonts,
      color,
      customTheme,
    });
    return res.send(result);
  } catch (error) {
    logger.error({
      errorMessage: error.message,
      stack: error.stack,
      ...req.params,
      ...req.query,
      ...req.body,
    });
    return res.status(500).send({ message: 'Unexpected error' });
  }
});

const templatePreviewHandler = async (req, res) => {
  try {
    const { params, query } = req;
    const { templateId } = params;
    const { color, account = 44, theme } = query;
    const fonts = await getFonts(account);
    const template = await getMasterTemplate(templateId);
    const themeKitName = theme || template.themeKit?.id || template.themeKit?.name;
    let customTheme;
    let campaignId = templateId;
    if (themeKitName) {
      customTheme = await getCustomTheme({ account, name: themeKitName });
      campaignId = `${campaignId}-${templateId}`;
    }
    const result = await renderTemplatePreview({
      template,
      campaignId,
      fonts,
      color,
      customTheme,
    });
    res.send(result);
  } catch (err) {
    logger.error(
      { errorMessage: err.message, stack: err.stack, ...req.params, ...req.query },
      '[SSR] Template preview error',
    );
    res.status(500).send({ message: 'Unexpected error' });
  }
};

const templateContentHandler = async (req, res) => {
  try {
    const { params, query } = req;
    const { templateId } = params;
    const { color, account = 44, theme, isAdmin = false, language } = query;
    const fonts = await getFonts(account);
    let template = await getMasterTemplate(templateId);
    if (language) {
      await translate(template, language);
    }
    const themeKitName = theme || template.themeKit?.id || template.themeKit?.name;
    let customTheme;
    let campaignId = templateId;
    if (themeKitName) {
      customTheme = await getCustomTheme({ account, name: themeKitName });
      campaignId = `${campaignId}-${templateId}`;
    }

    const selectedImage = await getSelectedImage({
      collection: 'user_images',
      name: customTheme?.logo?.current,
      databaseId: Number(account),
    });

    const ogLogoName = getNameFromURL(customTheme?.logo?.original);
    const currentLogoName = getNameFromURL(customTheme?.logo?.current);

    if (ogLogoName && selectedImage && ogLogoName !== currentLogoName) {
      template = replaceImages({
        template,
        imageData: {
          templateName: template.displayName,
          currentImage: customTheme.logo.original,
          selectedImage: { ...selectedImage, id: selectedImage._id.toString() },
        },
      });
      template?.images?.push?.({
        _id: selectedImage._id.toString(),
        name: selectedImage.name,
        url: selectedImage.url,
        height: selectedImage.height,
        width: selectedImage.width,
        __typename: 'Image',
      });
    } else if (!selectedImage && !customTheme?.logo?.current && customTheme?.logo?.original) {
      logoHide(template.elements, customTheme.logo.original);
    }

    const result = await renderResult({
      template,
      campaignId,
      fonts,
      color,
      customTheme,
      isAdmin: !!isAdmin,
    });
    res.send(result);
  } catch (err) {
    logger.error(
      { errorMessage: err.message, stack: err.stack, ...req.params, ...req.query },
      '[SSR] Template content error',
    );
    res.status(500).send({ message: 'Unexpected error' });
  }
};

router
  .route('/template-content/:templateId')
  .get(templateContentHandler)
  .post(templateContentHandler);

router.route('/template-preview/:templateId').get(templatePreviewHandler);

router.get('/thumbnail/:templateId', async ({ params, query }, res) => {
  try {
    const { templateId: tplIdWithOptionalExtension } = params;
    const templateId = (tplIdWithOptionalExtension || '').split('.')[0];
    const { color, account = 44, theme, language } = query;
    const template = await getMasterTemplate(templateId);
    if (language) {
      await translate(template, language);
    }
    const fonts = account ? await getFonts(account) : baseFonts;

    const themeKitName = theme || template.themeKit?.id || template.themeKit?.name;

    let customTheme = null;
    let campaignId = templateId;
    if (themeKitName) {
      customTheme = await getCustomTheme({ account, name: themeKitName });
      campaignId = `${campaignId}-${templateId}`;
    }
    let result = await thumbnailRender({
      template,
      campaignId,
      fonts,
      color,
      customTheme,
    });
    result = imageUtil.replaceS3UrlWithCdn(result);
    res.send(result);
  } catch (err) {
    if (err.message === 'No boxes in page') {
      logger.warn({
        errorMessage: err.message,
        ...params,
      });
      return res.status(422).send({ message: 'Unprocessable entity', error: err.message });
    }

    if (err.message.includes('No translation')) {
      return res.status(400).send({ message: 'no_translation' });
    }

    logger.error(
      {
        errorMessage: err.message,
        stack: err.stack,
        ...params,
        ...query,
        requestUrl: err?.config?.url || null,
      },
      '[SSR] Create thumbnail error',
    );
    res.status(500).send({ message: 'Unexpected error' });
  }
});

const loadVariant = async (databaseId, variantId) => {
  const id = mongoose.Types.ObjectId(variantId);
  const db = mongoose.connection.db;
  const campaignCollection = `${databaseId}_campaigns`;

  const features = await getAccountFeatures(databaseId);
  const variantTemplate = await db
    .collection(campaignCollection)
    .aggregate([
      { $match: { 'variants._id': id } },
      { $unwind: '$variants' },
      { $match: { 'variants._id': id } },
      {
        $project: {
          template: `$variants.template`,
          id: 1,
          locale: 1,
          templateName: 1,
          version: 1,
        },
      },
    ])
    .toArray();

  if (!variantTemplate.length) return false;

  let { template, id: campaignId, locale, templateName, version } = variantTemplate[0];

  if (features.includes('VARIANT_TEMPLATE_MIGRATED')) {
    const variantTemplate = await db
      .collection('user_variant_templates')
      .findOne({ variantId: id });

    if (variantTemplate) {
      template = variantTemplate.template;
    }
  }

  return { template, campaignId, locale, templateName, version };
};

router.get('/preview/:databaseId/:variantId', async ({ params }, res) => {
  try {
    const { databaseId, variantId } = params;
    const { template, campaignId, version } = await loadVariant(databaseId, variantId);
    if (parseInt(version, 10) === 1) {
      res.status(400).send({ message: 'Version 1 campaign not supported' });
      return;
    }
    const [fonts, features] = await Promise.all([
      getFonts(databaseId),
      getAccountFeatures(databaseId),
    ]);
    const result = await thumbnailRender({
      template,
      campaignId: `${campaignId}-${variantId}`,
      variantId,
      databaseId,
      fonts,
      features,
      type: 'preview-thumbnail',
    });
    res.send(result);
  } catch (err) {
    if (err.message === 'No boxes in page' || err.message === 'Too many elements in template') {
      logger.warn({
        errorMessage: err.message,
        ...params,
      });
      return res.status(422).send({ message: 'Unprocessable entity', error: err.message });
    }

    logger.error(
      { errorMessage: err.message, stack: err.stack, ...params },
      '[SSR] Create preview error',
    );

    res.status(500).send({ message: 'Unexpected error' });
  }
});

const OUTRUNNED_THEME_ALTERNATIVE = {
  Clean: 'Essential',
  Colorful: 'Edge',
  Minimal: 'Pure',
  Energetic: 'Spark',
  Prestige: 'Botanic',
};

const getUserLatestCustomTheme = async ({ databaseId, db }) => {
  const criteria = { databaseId, sourceTheme: { $exists: true } };

  const customThemes = await db
    .collection('master_custom_themes')
    .find(criteria)
    .sort({ _id: -1 })
    .limit(1)
    .toArray();
  const sourceThemeId = customThemes?.[0]?.sourceTheme;
  const sourceTheme = await db.collection('master_custom_themes').findOne({ _id: sourceThemeId });
  const customThemeName = sourceTheme?.name || 'Essential';
  const themeName = OUTRUNNED_THEME_ALTERNATIVE?.[customThemeName] ?? customThemeName;

  const customTheme =
    customThemes?.[0] ??
    (await db
      .collection('master_custom_themes')
      .findOne({ databaseId: 44, sourceTheme: { $exists: false }, name: 'Essential' }));

  return { customTheme, themeName };
};

const getPreferredTemplate = async ({ useCaseId, themeName, db, skip = 0 }) => {
  // const useCase = await db
  //   .collection('master_usecases')
  //   .findOne({ _id: mongoose.Types.ObjectId(useCaseId) });

  // These templates not includes template property that's why getMasterTemplate called
  const useCaseTemplates = await db
    .collection('master_templates')
    .find({ useCase: mongoose.Types.ObjectId(useCaseId), theme: themeName })
    .skip(skip)
    .toArray();

  const interstitial = useCaseTemplates?.find?.(({ type }) =>
    ['fullscreen', 'interstitial'].includes(type),
  );
  const popup = interstitial || useCaseTemplates?.find?.(({ type }) => type === 'popup');
  const sidemessage = popup || useCaseTemplates?.find?.(({ type }) => type === 'sidebar');
  const nanobar = sidemessage || useCaseTemplates?.find?.(({ type }) => type === 'nanobar');
  const preferredTemplate = nanobar || useCaseTemplates?.find?.(({ type }) => type === 'embedded');

  const template = await getMasterTemplate(preferredTemplate?._id ?? useCase?.templates?.[0]);

  return template;
};

router.get('/user-theme/:accountId/:useCaseId', async (req, res) => {
  const { accountId, useCaseId } = req.params;
  const { skip: skipStr, logo } = req.query;
  const databaseId = Number(accountId);
  const db = mongoose.connection.db;
  const context = {
    databaseId,
    useCaseId,
    skip: Number(skipStr),
    db,
  };
  const fonts = await getFonts(databaseId);
  const { customTheme, themeName } = await getUserLatestCustomTheme(context);
  const colors = [
    customTheme?.themeKit?.colors?.mainColor,
    ...customTheme?.themeKit?.colors?.secondaryColors,
  ];
  let template = await getPreferredTemplate({ ...context, themeName });

  const shouldHandleLogo = logo === '1';
  const selectedImage = shouldHandleLogo
    ? await getSelectedImage({
        collection: 'user_images',
        name: customTheme?.logo?.current,
        databaseId,
      })
    : null;

  if (customTheme?.logo?.original && selectedImage) {
    template = replaceImages({
      template,
      imageData: {
        templateName: template.displayName,
        currentImage: customTheme.logo.original,
        selectedImage: { ...selectedImage, id: selectedImage._id.toString() },
      },
    });
    template?.images?.push?.({
      _id: selectedImage._id.toString(),
      name: selectedImage.name,
      url: selectedImage.url,
      height: selectedImage.height,
      width: selectedImage.width,
      __typename: 'Image',
    });
  } else if (
    shouldHandleLogo &&
    !selectedImage &&
    !customTheme?.logo?.current &&
    customTheme?.logo?.original
  ) {
    logoHide(template.elements, customTheme.logo.original);
  }

  let result = await renderResult({
    template,
    databaseId,
    campaignId: '1',
    variantId: '1',
    fonts,
    customTheme,
  });
  result = imageUtil.replaceS3UrlWithCdn(result);

  result = result.replace(
    '<html',
    `<html data-custom-logo="${selectedImage?.url ?? ''}" data-custom-colors="${colors.join(';')}"`,
  );

  res.send(result);
});

router.get('/:databaseId/:variantId', async (req, res) => {
  try {
    const { databaseId } = req.params;
    let { variantId } = req.params;
    variantId = variantId.replace('.html', '');
    const variant = await loadVariant(databaseId, variantId);

    if (!variant) return res.status(404).send({ message: 'Cannot find variant' });

    const { template, locale, campaignId, templateName } = variant;
    const [poweredBy, fonts, features] = await Promise.all([
      getPoweredBy(databaseId, locale),
      getFonts(databaseId),
      getAccountFeatures(databaseId),
    ]);

    let result = await renderResult({
      template,
      campaignId,
      databaseId,
      variantId,
      fonts,
      poweredBy,
      onlyContent: true,
      templateName,
      locale,
      features,
      type: 'frontend',
    });

    result = imageUtil.replaceS3UrlWithCdn(result);

    return res.send(result);
  } catch (err) {
    if (err.message === 'No boxes in page' || err.message === 'Too many elements in template') {
      logger.warn({
        errorMessage: err.message,
        ...req.params,
      });
      return res.status(422).send({ message: 'Unprocessable entity', error: err.message });
    }

    logger.error(
      { errorMessage: err.message, stack: err.stack, ...req.params },
      'Error in getVariant by ID: ',
    );
    return res.status(500).send({ message: 'Unexpected error' });
  }
});

export default router;

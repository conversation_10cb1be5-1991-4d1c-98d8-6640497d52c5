const { mock } = require('@om/queries/src/testUtils');
const { materializedViewSchema, ordersSchema } = require('@om/queries');
const { BigQueryClient } = require('../../../services/bigQueryAdapter');
const { getQuery, getValueCondition, getGoalQueryByExperiment } = require('./getGoalQuery');
const {
  simpleGoalQuery,
  simpleRule,
  oneLevelOrRule,
  oneLevelOrQuery,
  eventFactory,
  orderFactory,
  experimentQueryForSimpleRule,
  experimentQueryComplexRule,
  simpleSmartAbTestGoalQuery,
  customEventGoal,
  customEventGoalWithoutProp,
  customEventQueryWithoutProp,
  mutipleRangeSmartAbTestGoalQuery,
  ranges,
  nonstandardOrderQuery,
  simpleSplitUrlTestQuery,
  complexSplitUrlTestQuery,
} = require('./testFixtures');

jest.setTimeout(120000);

jest.mock('../../account/account.resolvers', () => {
  return {
    Query: { getDomain: jest.fn() },
  };
});

jest.mock('./util', () => {
  return {
    BQ_PROJECT_ID: 'test_bq_project',
  };
});

const _metricsByDeviceType = ({ mobile = 0, desktop = 0, allType = 0 }) => {
  return { mobile, desktop, allType };
};

describe('unit tests', () => {
  describe('value condition tests', () => {
    test('should handle contains', () => {
      const result = getValueCondition({ operator: 'contains', value: 'cart', expressionIndex: 0 });
      expect(result).toEqual({
        params: {
          goal_value_0: '%cart%',
        },
        template: 'AND goal.value LIKE @goal_value_0',
      });
    });
    test('should handle not contains', () => {
      const result = getValueCondition({
        operator: 'notContains',
        value: 'cart',
        expressionIndex: 0,
      });
      expect(result).toEqual({
        params: {
          goal_value_0: '%cart%',
        },
        template: 'AND goal.value NOT LIKE @goal_value_0',
      });
    });
    test('should handle equals', () => {
      const result = getValueCondition({
        operator: 'equals',
        value: '50',
        expressionIndex: 0,
      });
      expect(result).toEqual({
        params: {
          goal_value_0: '50',
        },
        template: 'AND goal.value = @goal_value_0',
      });
    });
    test('should handle not equals', () => {
      const result = getValueCondition({
        operator: 'notEquals',
        value: '50',
        expressionIndex: 0,
      });
      expect(result).toEqual({
        params: {
          goal_value_0: '50',
        },
        template: 'AND goal.value != @goal_value_0',
      });
    });
    test('should handle interval', () => {
      const result = getValueCondition({
        operator: 'interval',
        value: [10, 50],
        expressionIndex: 0,
      });
      expect(result).toEqual({
        params: {
          goal_value_0_0: 10,
          goal_value_0_1: 50,
        },
        template:
          'AND SAFE_CAST(goal.value AS FLOAT64) BETWEEN @goal_value_0_0 AND @goal_value_0_1',
      });
    });
    test('should handle greaterThanEquals', () => {
      const result = getValueCondition({
        operator: 'greaterThanEquals',
        value: 33,
        expressionIndex: 3,
      });
      expect(result).toEqual({
        params: {
          goal_value_3: 33,
        },
        template: 'AND SAFE_CAST(goal.value AS FLOAT64) >= @goal_value_3',
      });
    });
    test('should handle lessThanEquals', () => {
      const result = getValueCondition({
        operator: 'lessThanEquals',
        value: 37,
        expressionIndex: 2,
      });
      expect(result).toEqual({
        params: {
          goal_value_2: 37,
        },
        template: 'AND SAFE_CAST(goal.value AS FLOAT64) <= @goal_value_2',
      });
    });
    test('should handle greaterThan', () => {
      const result = getValueCondition({
        operator: 'greaterThan',
        value: 33,
        expressionIndex: 3,
      });
      expect(result).toEqual({
        params: {
          goal_value_3: 33,
        },
        template: 'AND SAFE_CAST(goal.value AS FLOAT64) > @goal_value_3',
      });
    });
    test('should handle lessThan', () => {
      const result = getValueCondition({
        operator: 'lessThan',
        value: 3,
        expressionIndex: 1,
      });
      expect(result).toEqual({
        params: {
          goal_value_1: 3,
        },
        template: 'AND SAFE_CAST(goal.value AS FLOAT64) < @goal_value_1',
      });
    });
    test('should throw error for unknown operator', () => {
      expect(() => {
        getValueCondition({
          operator: 'unknown operator',
          value: 3,
          expressionIndex: 1,
        });
      }).toThrow(new Error('Unknown operator'));
    });
    test('should throw error if not enough values for interval', () => {
      expect(() => {
        getValueCondition({
          operator: 'interval',
          value: [1],
          expressionIndex: 1,
        });
      }).toThrow(new Error('Between operator requires two values'));
    });
    test('should handle startsWith', () => {
      const result = getValueCondition({
        operator: 'startsWith',
        value: 'testshop',
        expressionIndex: 1,
      });
      expect(result).toEqual({
        params: {
          goal_value_1: 'testshop',
        },
        template: 'AND STARTS_WITH(goal.value, @goal_value_1)',
      });
    });
    test('should handle not startsWith', () => {
      const result = getValueCondition({
        operator: 'notStartsWith',
        value: 'testshop',
        expressionIndex: 1,
      });
      expect(result).toEqual({
        params: {
          goal_value_1: 'testshop',
        },
        template: 'AND NOT STARTS_WITH(goal.value, @goal_value_1)',
      });
    });
    test('should handle endsWith', () => {
      const result = getValueCondition({
        operator: 'endsWith',
        value: 'testshop',
        expressionIndex: 1,
      });
      expect(result).toEqual({
        params: {
          goal_value_1: 'testshop',
        },
        template: 'AND ENDS_WITH(goal.value, @goal_value_1)',
      });
    });
    test('should handle not endsWith', () => {
      const result = getValueCondition({
        operator: 'notEndsWith',
        value: 'testshop',
        expressionIndex: 1,
      });
      expect(result).toEqual({
        params: {
          goal_value_1: 'testshop',
        },
        template: 'AND NOT ENDS_WITH(goal.value, @goal_value_1)',
      });
    });
  });

  describe('goal by campaign unit tests', () => {
    test('should generate correct query for simple goal', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: simpleRule,
        provider: 'shopify',
        uniqueConversions: true,
      });
      expect(query).normalizedEquals(simpleGoalQuery);
      expect(params).toEqual({
        goal_value_0: '%cart%',
        goal_field_0: 'pageView_url',
      });
    });

    test('should generate correct query for a goal for 3 subgoals with OR condition', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: oneLevelOrRule,
        provider: 'shopify',
        uniqueConversions: true,
        startDate: '2024-01-22',
        endDate: '2024-01-22',
      });
      expect(query).normalizedEquals(oneLevelOrQuery);
      expect(params).toEqual({
        goal_field_0: 'pageView_url',
        goal_field_1: 'pageView_url',
        goal_field_2: 'eoo_total',
        goal_value_0: '%cart%',
        goal_value_1: '%pricing%',
        goal_value_2: 50,
      });
    });

    test('should generate correct query for custom event goal without property', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: customEventGoalWithoutProp,
        provider: 'shopify',
        uniqueConversions: true,
      });
      expect(query).normalizedEquals(customEventQueryWithoutProp);
      expect(params).toEqual({
        goal_field_0: 'custom:order',
      });
    });

    test('should generate correct query for nonstandard order goal', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: {
          expressions: [
            {
              type: 'nonstandardOrder',
            },
          ],
        },
        provider: 'shopify',
        uniqueConversions: true,
      });
      expect(query).normalizedEquals(nonstandardOrderQuery);
      expect(params).toEqual({
        goal_field_0: 'nonstandardOrder',
      });
    });
  });

  describe('experiment unit tests', () => {
    test('should generate correct query experiment for simple rule', () => {
      const { query, params } = getGoalQueryByExperiment({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: simpleRule,
        experimentId: 'abc123',
        experimentStartedAt: '2022-12-22 07:45:19.968Z',
      });

      expect(query).normalizedEquals(experimentQueryForSimpleRule);
      expect(params).toEqual({
        goal_value_0: '%cart%',
        goal_field_0: 'pageView_url',
        experimentId: 'abc123',
        experimentStartedAt: '2022-12-22 07:45:19.968Z',
      });
    });

    test('should generate correct query experiment for complex rule', () => {
      const { query, params } = getGoalQueryByExperiment({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: oneLevelOrRule,
        experimentId: 'abc123',
        experimentStartedAt: '2022-12-22 07:45:19.968Z',
      });

      expect(query).normalizedEquals(experimentQueryComplexRule);
      expect(params).toEqual({
        goal_value_0: '%cart%',
        goal_field_0: 'pageView_url',
        goal_field_1: 'pageView_url',
        goal_field_2: 'eoo_total',
        goal_value_1: '%pricing%',
        goal_value_2: 50,
        experimentId: 'abc123',
        experimentStartedAt: '2022-12-22 07:45:19.968Z',
      });
    });
  });

  describe('smartAbTest unit tests', () => {
    test('should generate correct query for simple goal', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: simpleRule,
        provider: 'shopify',
        uniqueConversions: true,
        groupBy: 'smartAbTest',
        startDate: '2024-01-22T10:41:50.888Z',
        endDate: '2024-01-22T10:44:50.888Z',
      });
      expect(query).normalizedEquals(simpleSmartAbTestGoalQuery);
      expect(params).toEqual({
        goal_value_0: '%cart%',
        goal_field_0: 'pageView_url',
      });
    });
    test('should generate correct query for complex OR goal with multiple date ranges', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: oneLevelOrRule,
        provider: 'shopify',
        uniqueConversions: true,
        groupBy: 'smartAbTest',
        dateRanges: [
          {
            startDate: ranges[0].start,
            endDate: ranges[0].end,
          },
          {
            startDate: ranges[1].start,
            endDate: ranges[1].end,
          },
          {
            startDate: ranges[2].start,
            endDate: ranges[2].end,
          },
        ],
      });
      expect(query).normalizedEquals(mutipleRangeSmartAbTestGoalQuery);
      expect(params).toEqual({
        goal_value_0: '%cart%',
        goal_field_0: 'pageView_url',
        goal_field_1: 'pageView_url',
        goal_value_1: '%pricing%',
        goal_field_2: 'eoo_total',
        goal_value_2: 50,
      });
    });
  });

  describe('splitURLTest unit tests', () => {
    test('should generate correct query for simple goal', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: simpleRule,
        provider: 'shopify',
        groupBy: 'splitURLTest',
        startDate: '2025-01-20T09:13:21.749Z',
        endDate: '2025-02-20T09:13:21.749Z',
      });
      expect(query).normalizedEquals(simpleSplitUrlTestQuery);
      expect(params).toEqual({
        goal_value_0: '%cart%',
        goal_field_0: 'pageView_url',
      });
    });
    test('should generate correct query for a goal for 3 subgoals with OR condition', () => {
      const { query, params } = getQuery({
        materializedViewName: '_goal_events_123',
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules: oneLevelOrRule,
        provider: 'shopify',
        groupBy: 'splitURLTest',
        startDate: '2025-01-20T09:13:21.749Z',
        endDate: '2025-02-20T09:13:21.749Z',
      });
      expect(query).normalizedEquals(complexSplitUrlTestQuery);
      expect(params).toEqual({
        goal_field_0: 'pageView_url',
        goal_field_1: 'pageView_url',
        goal_field_2: 'eoo_total',
        goal_value_0: '%cart%',
        goal_value_1: '%pricing%',
        goal_value_2: 50,
      });
    });
  });
});

describe('integration tests', () => {
  describe('goal by campaign integration tests', () => {
    let bigqueryClient;
    const run = async ({
      events = [],
      orders = [],
      rules,
      startDate,
      endDate,
      uniqueConversions = false,
    } = {}) => {
      const { query, params } = getQuery({
        materializedViewName: mock(events, materializedViewSchema),
        ordersTableName: mock(orders, ordersSchema),
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules,
        provider: 'shopify',
        startDate,
        endDate,
        uniqueConversions,
      });

      return bigqueryClient.runQuery(query, { params });
    };
    beforeAll(() => {
      bigqueryClient = new BigQueryClient();
    });
    test('should return empty for empty tables.', async () => {
      const events = [];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([]);
    });

    test('should return correct values for simple goal with single impression', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should return correct values for simple goal with multiple impressions in attribution window for same variant', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:20:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should return correct values for simple goal with multiple events and impressions in attribution window for different variants', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:32:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart2',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:20:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd1',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'eoi_variantId',
          value: 'variant4321',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 2 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 2 }),
            variantId: 'variant1234',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 2 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            variantId: 'variant4321',
          }),
        ]),
      );
    });

    test('should return unique visitor goals', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'eoi_variantId',
          value: 'variant4321',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:20:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:32:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart2',
        }),

        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd1',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:30:01.000Z',
          deviceId: 'd1',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),

        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd2',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd2',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd2',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd2',
          field: 'eoi_variantId',
          value: 'variant4321',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:40:01.000Z',
          deviceId: 'd2',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart2',
        }),

        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd3',
          field: 'eoi_variantId',
          value: 'variant4321',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd3',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:45:01.000Z',
          deviceId: 'd3',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart2',
        }),

        eventFactory({
          timestamp: '2023-04-15T03:21:01.000Z',
          deviceId: 'd4',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 4 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 5 }),
            variantId: 'variant1234',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 3 }),
            variantId: 'variant4321',
          }),
        ]),
      );
    });

    test('should return 0 for simple goal when there are no impressions in attribution window', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-16T03:10:01.000Z',
          field: 'eoi_variantId',
          value: 'variant4321',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({}),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            variantId: 'variant4321',
          }),
        ]),
      );
    });

    test('should return correct goal values for complex goal with single impression for every partial goal', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:35:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
        }),
        eventFactory({ timestamp: '2023-04-15T03:40:01.000Z', field: 'eoo_total', value: '78' }),
        eventFactory({
          timestamp: '2023-04-15T03:20:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:40:01.000Z', total: '78' })];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 3 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should return correct goal values for complex goal with multiple impressions for partial goals', async () => {
      const events = [
        // this pageview is attributable to two impressions with two different variants
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // this eoo is attributable to two impressions too, to two variants
        eventFactory({ timestamp: '2023-04-15T03:35:01.000Z', field: 'eoo_total', value: '78' }),
        eventFactory({
          timestamp: '2023-04-15T03:02:01.000Z',
          field: 'eoi_variantId',
          value: 'variant2',
        }),
        // multiple impressions of the same variant should not increase goal count
        eventFactory({
          timestamp: '2023-04-15T03:02:35.000Z',
          field: 'eoi_variantId',
          value: 'variant2',
        }),
        // this pageview is attributable to only one impression
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:35:01.000Z', total: '78' })];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            variantId: 'variant1',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 2 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            variantId: 'variant2',
          }),
        ]),
      );
    });

    test('should return correct goal values if one partial or result is missing corresponding impression', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({ timestamp: '2023-04-15T03:03:01.000Z', field: 'eoo_total', value: '78' }),
        eventFactory({
          timestamp: '2023-04-15T03:02:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:02:35.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:03:01.000Z', total: '78' })];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 2 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
          variantId: 'variant1',
        }),
      ]);
    });

    test('should return correct goal values if deviceId in impression is different', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:02:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
          deviceId: 'otherdeviceId',
        }),
      ];

      const actual = await run({ events, rules: oneLevelOrRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 2 }),
          variantId: 'variant1',
        }),
      ]);
    });

    test('should return correct goal values if impression is outside of 5 day attribution window', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:02:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
        }),
        eventFactory({
          timestamp: '2023-04-10T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
      ];

      const actual = await run({ events, rules: oneLevelOrRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
          variantId: 'variant1',
        }),
      ]);
    });

    test('should return correct goal values if only for the queried interval', async () => {
      const events = [
        // this goal is also outside of the queried interval, does not count
        eventFactory({
          timestamp: '2023-04-19T03:10:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // this eoi is outside of queried interval
        eventFactory({
          timestamp: '2023-04-18T03:05:01.000Z',
          field: 'eoi_variantId',
          value: 'variant0',
        }),
        // this goal is inside the queried interval
        eventFactory({ timestamp: '2023-04-15T03:10:01.000Z', field: 'eoo_total', value: '51' }),
        // this eoi is inside the attribution window
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
        // this goal was outside of the queried window, it does not count, even though it has an attributable impression
        eventFactory({
          timestamp: '2023-04-12T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // this eoi is inside the attribution window, it counts, even if outside of queried goal interval
        eventFactory({
          timestamp: '2023-04-11T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant2',
        }),
        // this eoi is outside of the attribution window, should not count
        eventFactory({
          timestamp: '2023-04-10T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant3',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:10:01.000Z', total: '78' })];
      const actual = await run({
        events,
        orders,
        rules: oneLevelOrRule,
        startDate: '2023-04-15',
        endDate: '2023-04-17',
      });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 1 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            variantId: 'variant1',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 1 }),
            uniqueVisitorCount: _metricsByDeviceType({}),
            variantId: 'variant2',
          }),
        ]),
      );
    });

    test('should use only unified table and skip eoo in mat view', async () => {
      const events = [
        // this eoo will be ignored
        eventFactory({ timestamp: '2023-04-15T03:03:01.000Z', field: 'eoo_total', value: '78' }),
        eventFactory({
          timestamp: '2023-04-15T03:02:35.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
      ];
      const actual = await run({ events, orders: [], rules: oneLevelOrRule });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({}),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            variantId: 'variant1',
          }),
        ]),
      );
    });

    test('should only use orders for specific user', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:02:35.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
      ];
      const orders = [
        orderFactory({ timestamp: '2023-04-15T03:03:01.000Z', accountId: '456', total: '78' }),
      ];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual(expect.arrayContaining([]));
    });

    test('should calculate custom goal conversion correctly', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'v1',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'v1',
          deviceId: 'd2',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'v1',
          deviceId: 'd3',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'custom:order_total',
          value: '500',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          field: 'custom:order_total',
          value: '300',
          deviceId: 'd2',
          deviceType: 'desktop',
        }),
      ];
      const actual = await run({ events, rules: customEventGoal });
      expect(actual).toEqual(
        expect.arrayContaining([
          {
            goalCount: _metricsByDeviceType({ allType: 2, desktop: 2 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 3, desktop: 3 }),
            variantId: 'v1',
          },
        ]),
      );
    });

    test('should calculate custom goal (event exists)', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'v1',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'v1',
          deviceId: 'd2',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'v1',
          deviceId: 'd3',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'custom:order',
          value: null,
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          field: 'custom:order',
          value: null,
          deviceId: 'd2',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          field: 'custom:order_total',
          value: 300,
          deviceId: 'd2',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          field: 'custom:order',
          value: null,
          deviceId: 'd3',
          deviceType: 'desktop',
        }),
      ];
      const actual = await run({ events, rules: customEventGoalWithoutProp });
      expect(actual).toEqual(
        expect.arrayContaining([
          {
            goalCount: _metricsByDeviceType({ allType: 3, desktop: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 3, desktop: 3 }),
            variantId: 'v1',
          },
        ]),
      );
    });

    test('should return correct values for different device type', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceType: 'desktop',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'desktop',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceType: 'mobile',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'mobile',
          deviceId: 'd2',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 2, desktop: 1, mobile: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 2, desktop: 1, mobile: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should return correct goal values for complex goal with mobile events', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceType: 'mobile',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:35:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
          deviceType: 'mobile',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:40:01.000Z',
          field: 'eoo_total',
          value: '78',
          deviceType: 'mobile',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:20:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'mobile',
        }),
      ];
      const orders = [
        orderFactory({ timestamp: '2023-04-15T03:40:01.000Z', total: '78', deviceType: 'mobile' }),
      ];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 3, mobile: 3 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1, mobile: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should return correct values for different device with same device id', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceType: 'desktop',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'desktop',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:03:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceType: 'mobile',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:02:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'mobile',
          deviceId: 'd1',
        }),
      ];
      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 2, desktop: 1, mobile: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1, desktop: 1, mobile: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should return correct values with www. or m. providerServiceId', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-13T03:00:01.000Z',
          field: 'eoi_variantId',
          value: '123',
        }),
        eventFactory({
          timestamp: '2023-04-13T03:00:02.000Z',
          field: 'eoi_variantId',
          value: '123',
        }),
      ];
      const orders = [
        orderFactory({ timestamp: '2023-04-15T03:03:01.000Z', accountId: '123456', total: '78' }),
        orderFactory({
          timestamp: '2023-04-15T03:03:02.000Z',
          providerServiceId: 'www.lions-not-sheep.myshopify.com',
          accountId: '123456',
          total: '78',
        }),
      ];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 2, desktop: 0, mobile: 0 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 1, desktop: 0, mobile: 0 }),
          variantId: '123',
        }),
      ]);
    });

    test('should return correct values for nonstandard order goals', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'desktop',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceType: 'mobile',
          deviceId: 'd2',
        }),
      ];

      const actual = await run({
        uniqueConversions: true,
        events,
        orders: [
          orderFactory({
            timestamp: '2023-04-15T03:03:02.000Z',
            deviceId: 'd1',
            orderId: null,
            total: null,
            currency: null,
            itemCount: null,
            deviceType: 'desktop',
            source: 'ga4_nonstandard',
          }),
          orderFactory({
            timestamp: '2023-04-15T03:03:02.001Z',
            deviceId: 'd1',
            orderId: null,
            total: null,
            currency: null,
            itemCount: null,
            deviceType: 'desktop',
            source: 'ga4_nonstandard',
          }),
          orderFactory({
            timestamp: '2023-04-15T03:03:02.002Z',
            deviceId: 'd2',
            orderId: null,
            total: null,
            currency: null,
            itemCount: null,
            deviceType: 'mobile',
            source: 'ga4_nonstandard',
          }),
        ],
        rules: {
          expressions: [
            {
              type: 'nonstandardOrder',
            },
          ],
        },
      });
      expect(actual).toEqual([
        expect.objectContaining({
          goalCount: _metricsByDeviceType({ allType: 2, desktop: 1, mobile: 1 }),
          uniqueVisitorCount: _metricsByDeviceType({ allType: 2, desktop: 1, mobile: 1 }),
          variantId: 'variant1234',
        }),
      ]);
    });
  });

  describe('experiment integration tests', () => {
    let bigqueryClient;
    const run = async ({
      events = [],
      orders = [],
      rules,
      experimentId = '64a2d9bf3f9eac2bc4888e1b',
      experimentStartedAt = '2022-12-01 07:45:19.968Z',
      uniqueConversions = false,
    } = {}) => {
      const { query, params } = getGoalQueryByExperiment({
        materializedViewName: mock(events, materializedViewSchema),
        ordersTableName: mock(orders, ordersSchema),
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules,
        experimentId,
        experimentStartedAt,
        uniqueConversions,
      });

      return bigqueryClient.runQuery(query, { params });
    };
    beforeAll(() => {
      bigqueryClient = new BigQueryClient();
    });

    test('should return correct values for experiment groups (two groups, no campaigns, simple goal)', async () => {
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // events with no visitorInExperiment don't count
        eventFactory({
          timestamp: '2022-12-02T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment: '[]',
        }),
        eventFactory({
          timestamp: '2022-12-22T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment: null,
        }),
        // event in another experiment does not count
        eventFactory({
          timestamp: '2022-12-21T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment:
            '[{ "experimentId": "some other id", "groupId": "some other group" }]',
        }),
        // event in same experiment, but other group counts
        eventFactory({
          timestamp: '2022-12-21T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        // event with same deviceId, different timestamp counts
        eventFactory({
          timestamp: '2022-12-21T04:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
      ];

      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 2,
          campaigns: [],
        },
        {
          groupId: 'other_group',
          goalCount: 1,
          campaigns: [],
        },
      ]);
    });

    test('should return correct values for experiment groups and campaigns(two groups, 1-1 different campaigns)', async () => {
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
        }),
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '456',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-04T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // event in same experiment, but other group counts
        eventFactory({
          timestamp: '2022-12-05T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
      ];

      const actual = await run({ events, rules: simpleRule });
      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 1,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 1,
            },
          ],
        },
        {
          groupId: 'other_group',
          goalCount: 1,
          campaigns: [
            {
              campaignId: '456',
              goalCount: 1,
            },
          ],
        },
      ]);
    });

    test('should return correct values for experiment groups and campaigns(multiple groups, same campaign in multiple groups)', async () => {
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
        }),
        eventFactory({
          timestamp: '2022-12-04T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '456',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-05T02:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-07T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        eventFactory({
          timestamp: '2022-12-08T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        // event in same experiment, but other group counts
        eventFactory({
          timestamp: '2022-12-05T03:30:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
      ];

      const actual = await run({ events, rules: simpleRule });

      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 1,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 1,
            },
          ],
        },
        {
          groupId: 'other_group',
          goalCount: 2,
          campaigns: [
            {
              campaignId: '456',
              goalCount: 2,
            },
            {
              campaignId: '123',
              goalCount: 2,
            },
          ],
        },
      ]);
    });

    test('should return correct values for complex goal with multiple events, and multiple groups and campaigns', async () => {
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
        }),
        eventFactory({
          timestamp: '2022-12-03T04:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
          deviceId: 'abc',
        }),
        // event with different device id than eoi does not count to campaign, only group count
        eventFactory({
          timestamp: '2022-12-05T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          // this user has not seen any campaigns
          deviceId: '345',
        }),
        // this again has a matching impression
        eventFactory({
          timestamp: '2022-12-05T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/pricing',
          deviceId: 'abc',
        }),
        // same campaign in other group
        eventFactory({
          timestamp: '2022-12-05T03:05:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-05T08:05:01.000Z',
          field: 'eoo_total',
          value: 100,
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        // other campaign in other group
        eventFactory({
          timestamp: '2022-12-06T03:05:01.000Z',
          field: 'eoi_campaignId',
          value: '678',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-06T08:05:01.000Z',
          field: 'eoo_total',
          value: 150,
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
      ];
      const orders = [
        orderFactory({
          timestamp: '2022-12-05T08:05:01.000Z',
          total: 100,
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        orderFactory({
          timestamp: '2022-12-06T08:05:01.000Z',
          total: 150,
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
      ];

      const actual = await run({ events, orders, rules: oneLevelOrRule });

      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 2,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 1,
            },
          ],
        },
        {
          groupId: 'other_group',
          goalCount: 2,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 2,
            },
            {
              campaignId: '678',
              goalCount: 1,
            },
          ],
        },
      ]);
    });

    test('should use only unified table and skip eoo in mat view', async () => {
      const events = [
        // this eoo will be ignored
        eventFactory({ timestamp: '2023-04-15T03:03:01.000Z', field: 'eoo_total', value: '78' }),
        eventFactory({
          timestamp: '2023-04-15T03:02:35.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
      ];
      const actual = await run({ events, orders: [], rules: oneLevelOrRule });
      expect(actual).toEqual([]);
    });

    test('should only use orders for specific user', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:02:35.000Z',
          field: 'eoi_variantId',
          value: 'variant1',
        }),
      ];
      const orders = [
        orderFactory({ timestamp: '2023-04-15T03:03:01.000Z', accountId: '456', total: '78' }),
      ];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual(expect.arrayContaining([]));
    });

    test('should return correct values with www. or m. providerServiceId', async () => {
      const addresses = [
        {
          provider: 'optimonk',
          providerServiceId: '123456',
          customerAddress: 'f08d63fe-dac1-4fa1-865f-f9eb',
          addressType: 'deviceId',
        },
        {
          provider: 'shopify',
          providerServiceId: 'www.lions-not-sheep.myshopify.com',
          customerAddress: null,
          addressType: 'customerId',
        },
      ];
      const cleanAddresses = [
        {
          provider: 'optimonk',
          providerServiceId: '123456',
          customerAddress: 'f08d63fe-dac1-4fa1-865f-f9eb',
          addressType: 'deviceId',
        },
        {
          provider: 'shopify',
          providerServiceId: 'lions-not-sheep.myshopify.com',
          customerAddress: null,
          addressType: 'customerId',
        },
      ];
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
          addresses,
        }),
        eventFactory({
          timestamp: '2022-12-03T03:30:02.000Z',
          field: 'eoi_campaignId',
          value: '123',
          addresses: cleanAddresses,
        }),
      ];
      const orders = [
        orderFactory({ timestamp: '2023-04-15T03:03:01.000Z', accountId: '123456', total: '78' }),
        orderFactory({
          timestamp: '2023-04-15T03:03:02.000Z',
          providerServiceId: 'www.lions-not-sheep.myshopify.com',
          accountId: '123456',
          total: '78',
        }),
        orderFactory({
          timestamp: '2023-04-15T03:03:03.000Z',
          providerServiceId: 'm.lions-not-sheep.myshopify.com',
          accountId: '123456',
          total: '78',
        }),
      ];
      const actual = await run({ events, orders, rules: oneLevelOrRule });
      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 3,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 6,
            },
          ],
        },
      ]);
    });

    test('should return correct values for custom event goal in experiments', async () => {
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
        }),
        eventFactory({
          timestamp: '2022-12-04T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '456',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-05T02:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-07T03:30:01.000Z',
          field: 'custom:order',
          value: null,
        }),
        eventFactory({
          timestamp: '2022-12-08T03:30:01.000Z',
          field: 'custom:order',
          value: null,
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        // event in same experiment, but other group counts
        eventFactory({
          timestamp: '2022-12-05T03:30:01.000Z',
          field: 'custom:order',
          value: null,
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
      ];

      const actual = await run({ events, rules: customEventGoalWithoutProp });

      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 1,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 1,
            },
          ],
        },
        {
          groupId: 'other_group',
          goalCount: 2,
          campaigns: [
            {
              campaignId: '456',
              goalCount: 2,
            },
            {
              campaignId: '123',
              goalCount: 2,
            },
          ],
        },
      ]);
    });

    test('should return correct values for nonstandard goal in experiments', async () => {
      const events = [
        eventFactory({
          timestamp: '2022-12-03T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
        }),
        eventFactory({
          timestamp: '2022-12-04T03:30:01.000Z',
          field: 'eoi_campaignId',
          value: '456',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
        eventFactory({
          timestamp: '2022-12-05T02:30:01.000Z',
          field: 'eoi_campaignId',
          value: '123',
          visitorInExperiment:
            '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
        }),
      ];

      const actual = await run({
        events,
        orders: [
          orderFactory({
            timestamp: '2023-04-15T03:03:02.000Z',
            orderId: null,
            total: null,
            currency: null,
            itemCount: null,
            deviceType: 'desktop',
            source: 'ga4_nonstandard',
          }),
          orderFactory({
            timestamp: '2023-04-15T03:03:02.001Z',
            orderId: null,
            total: null,
            currency: null,
            itemCount: null,
            deviceType: 'desktop',
            source: 'ga4_nonstandard',
            visitorInExperiment:
              '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
          }),
          orderFactory({
            timestamp: '2023-04-15T03:03:02.002Z',
            orderId: null,
            total: null,
            currency: null,
            itemCount: null,
            source: 'ga4_nonstandard',
            visitorInExperiment:
              '[{ "experimentId": "64a2d9bf3f9eac2bc4888e1b", "groupId": "other_group" }]',
          }),
        ],
        rules: {
          expressions: [
            {
              type: 'nonstandardOrder',
            },
          ],
        },
      });

      expect(actual).toEqual([
        {
          groupId: '64a2d9bf3f9eac2bc4888e1d',
          goalCount: 1,
          campaigns: [
            {
              campaignId: '123',
              goalCount: 1,
            },
          ],
        },
        {
          groupId: 'other_group',
          goalCount: 2,
          campaigns: [
            {
              campaignId: '456',
              goalCount: 2,
            },
            {
              campaignId: '123',
              goalCount: 2,
            },
          ],
        },
      ]);
    });
  });

  describe('smartAbTest integration tests', () => {
    let bigqueryClient;
    const run = async ({
      events = [],
      orders = [],
      rules,
      dateRanges,
      startDate,
      endDate,
    } = {}) => {
      const { query, params } = getQuery({
        materializedViewName: mock(events, materializedViewSchema),
        ordersTableName: mock(orders, ordersSchema),
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules,
        provider: 'shopify',
        ...(dateRanges ? { dateRanges } : { startDate, endDate }),
        groupBy: 'smartAbTest',
      });

      return bigqueryClient.runQuery(query, { params });
    };
    beforeAll(() => {
      bigqueryClient = new BigQueryClient();
    });
    test('should return correct goal values for the queried interval', async () => {
      const events = [
        // this goal is also outside of the queried interval, does not count
        eventFactory({
          timestamp: '2023-04-19T03:10:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // this eoi is outside of queried interval, in the future
        eventFactory({
          timestamp: '2023-04-18T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123',
        }),
        // this goal is inside the queried interval
        eventFactory({ timestamp: '2023-04-15T03:10:01.000Z', field: 'eoo_total', value: '51' }),
        // this eoi is inside the attribution window
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123',
        }),
        // this goal was outside of the queried window, it does not count, even though it has an attributable impression
        eventFactory({
          timestamp: '2023-04-12T03:05:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
        }),
        // this eoi is inside the 5 day attribution window for goals by variant, does not count for smartAbTest
        eventFactory({
          timestamp: '2023-04-11T03:00:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'def456',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:10:01.000Z', total: '78' })];
      const actual = await run({
        events,
        orders,
        rules: oneLevelOrRule,
        startDate: '2023-04-15',
        endDate: '2023-04-17',
      });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 1 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            smartAbTest: 'abc123',
          }),
        ]),
      );
    });
    test('should handle multiple smartAbTestIdsInData', async () => {
      const events = [
        // this eoi is inside the attribution window
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'def456',
          deviceId: 'd4',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:11:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'def456',
        }),
        // this eoi is outside of the attribution window, should not count
        eventFactory({
          timestamp: '2023-04-15T02:58:00.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:02.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
          deviceId: 'd3',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:03.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456_ghi789',
          deviceId: 'd2',
        }),
      ];
      const orders = [
        orderFactory({ timestamp: '2023-04-15T03:15:01.000Z', total: '78' }),
        orderFactory({ timestamp: '2023-04-15T03:15:01.000Z', total: '100', deviceId: 'd2' }),
        orderFactory({ timestamp: '2023-04-15T03:15:01.000Z', total: '110', deviceId: 'd3' }),
      ];
      const actual = await run({
        events,
        orders,
        rules: oneLevelOrRule,
        startDate: '2023-04-15T03:00:00.000Z',
        endDate: '2023-04-15T04:00:00.000Z',
      });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 3 }),
            smartAbTest: 'abc123',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 4 }),
            smartAbTest: 'def456',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 1 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            smartAbTest: 'ghi789',
          }),
        ]),
      );
    });
    test('should handle multiple date ranges', async () => {
      const events = [
        // these events are in the first date range
        eventFactory({
          timestamp: '2023-04-11T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
        }),
        eventFactory({
          timestamp: '2023-04-11T03:10:01.000Z',
          field: 'pageView_url',
          value: 'cart',
        }),

        // these events are in the second date range
        eventFactory({
          timestamp: '2023-04-14T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
        }),
        eventFactory({
          timestamp: '2023-04-14T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-04-14T03:10:01.000Z',
          field: 'pageView_url',
          value: 'pricing',
        }),
        eventFactory({
          timestamp: '2023-04-14T04:10:01.000Z',
          field: 'pageView_url',
          value: 'cart',
          deviceId: 'd2',
        }),

        // these events are both in the first and second date range
        eventFactory({
          timestamp: '2023-04-13T11:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123',
          deviceId: 'd3',
        }),
        eventFactory({
          timestamp: '2023-04-13T11:10:01.000Z',
          field: 'pageView_url',
          value: 'pricing',
          deviceId: 'd3',
        }),

        // these events are in the third date range
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:10:01.000Z', total: '78' })];
      const actual = await run({
        events,
        orders,
        rules: oneLevelOrRule,
        dateRanges: [
          {
            startDate: '2023-04-10T00:00:00.000Z',
            endDate: '2023-04-13T12:00:00.000Z',
          },
          {
            startDate: '2023-04-13T00:00:00.000Z',
            endDate: '2023-04-14T23:59:59.999Z',
          },
          {
            startDate: '2023-04-15T00:00:00.000Z',
            endDate: '2023-04-17T23:59:59.999Z',
          },
        ],
      });

      expect(actual).toEqual(
        expect.arrayContaining([
          {
            startDate: '2023-04-10T00:00:00.000Z',
            endDate: '2023-04-13T12:00:00.000Z',
            results: expect.arrayContaining([
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 2 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 2 }),
                smartAbTest: 'abc123',
              }),
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 1 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
                smartAbTest: 'def456',
              }),
            ]),
          },
          {
            startDate: '2023-04-13T00:00:00.000Z',
            endDate: '2023-04-14T23:59:59.999Z',
            results: expect.arrayContaining([
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 3 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 3 }),
                smartAbTest: 'abc123',
              }),
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 2 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 2 }),
                smartAbTest: 'def456',
              }),
            ]),
          },
          {
            startDate: '2023-04-15T00:00:00.000Z',
            endDate: '2023-04-17T23:59:59.999Z',
            results: expect.arrayContaining([
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 1 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
                smartAbTest: 'abc123',
              }),
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 1 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
                smartAbTest: 'def456',
              }),
            ]),
          },
        ]),
      );
    });
    test('should return result even if there is no goal conversion so far', async () => {
      const events = [
        // these events are in the first date range
        eventFactory({
          timestamp: '2023-04-11T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
          deviceType: 'desktop',
        }),

        // these events are in the second date range
        eventFactory({
          timestamp: '2023-04-14T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-14T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
          deviceId: 'd2',
          deviceType: 'desktop',
        }),
        eventFactory({
          timestamp: '2023-04-14T03:10:01.000Z',
          field: 'pageView_url',
          value: 'pricing',
        }),
        eventFactory({
          timestamp: '2023-04-14T04:10:01.000Z',
          field: 'pageView_url',
          value: 'cart',
          deviceId: 'd2',
        }),
      ];
      const orders = [orderFactory({ timestamp: '2023-04-15T03:10:01.000Z', total: '78' })];
      const actual = await run({
        events,
        orders,
        rules: oneLevelOrRule,
        dateRanges: [
          {
            startDate: '2023-04-10T00:00:00.000Z',
            endDate: '2023-04-13T12:00:00.000Z',
          },
          {
            startDate: '2023-04-13T00:00:00.000Z',
            endDate: '2023-04-14T23:59:59.999Z',
          },
        ],
      });

      expect(actual).toEqual(
        expect.arrayContaining([
          {
            startDate: '2023-04-10T00:00:00.000Z',
            endDate: '2023-04-13T12:00:00.000Z',
            results: expect.arrayContaining([
              expect.objectContaining({
                goalCount: _metricsByDeviceType({}),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 1, desktop: 1 }),
                smartAbTest: 'abc123',
              }),
              expect.objectContaining({
                goalCount: _metricsByDeviceType({}),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 1, desktop: 1 }),
                smartAbTest: 'def456',
              }),
            ]),
          },
          {
            startDate: '2023-04-13T00:00:00.000Z',
            endDate: '2023-04-14T23:59:59.999Z',
            results: expect.arrayContaining([
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 2 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 2, desktop: 2 }),
                smartAbTest: 'abc123',
              }),
              expect.objectContaining({
                goalCount: _metricsByDeviceType({ allType: 2 }),
                uniqueVisitorCount: _metricsByDeviceType({ allType: 2, desktop: 2 }),
                smartAbTest: 'def456',
              }),
            ]),
          },
        ]),
      );
    });
    test('should handle nonstandard orders', async () => {
      const events = [
        // this eoi is inside the attribution window
        eventFactory({
          timestamp: '2023-04-15T03:05:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:10:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'def456',
          deviceId: 'd4',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:11:01.000Z',
          field: 'eoi_smartAbTest',
          value: 'def456',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:02.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456',
          deviceId: 'd3',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:03.000Z',
          field: 'eoi_smartAbTest',
          value: 'abc123_def456_ghi789',
          deviceId: 'd2',
        }),
      ];
      const orders = [
        orderFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          deviceId: 'd1',
          orderId: null,
          total: null,
          currency: null,
          itemCount: null,
          source: 'ga4_nonstandard',
        }),
        orderFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          deviceId: 'd2',
          orderId: null,
          total: null,
          currency: null,
          itemCount: null,
          source: 'ga4_nonstandard',
        }),
        orderFactory({
          timestamp: '2023-04-15T03:15:01.000Z',
          deviceId: 'd3',
          orderId: null,
          total: null,
          currency: null,
          itemCount: null,
          source: 'ga4_nonstandard',
        }),
      ];
      const actual = await run({
        events,
        orders,
        rules: {
          expressions: [
            {
              type: 'nonstandardOrder',
            },
          ],
        },
        startDate: '2023-04-15T03:00:00.000Z',
        endDate: '2023-04-15T04:00:00.000Z',
      });
      expect(actual).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 3 }),
            smartAbTest: 'abc123',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 3 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 4 }),
            smartAbTest: 'def456',
          }),
          expect.objectContaining({
            goalCount: _metricsByDeviceType({ allType: 1 }),
            uniqueVisitorCount: _metricsByDeviceType({ allType: 1 }),
            smartAbTest: 'ghi789',
          }),
        ]),
      );
    });
  });

  describe('splitURLTest integration tests', () => {
    let bigqueryClient;
    const run = async ({
      events = [],
      orders = [],
      rules,
      startDate,
      endDate,
      uniqueConversions = false,
    } = {}) => {
      const { query, params } = getQuery({
        materializedViewName: mock(events, materializedViewSchema),
        ordersTableName: mock(orders, ordersSchema),
        accountId: '123456',
        providerServiceId: 'lions-not-sheep.myshopify.com',
        rules,
        provider: 'shopify',
        startDate,
        endDate,
        uniqueConversions,
        groupBy: 'splitURLTest',
      });

      return bigqueryClient.runQuery(query, { params });
    };
    beforeAll(() => {
      bigqueryClient = new BigQueryClient();
    });

    test('should return correct goal conversion value', async () => {
      const events = [
        // device 1 without conversion event
        eventFactory({
          timestamp: '2023-04-15T03:00:03.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd1',
        }),

        // device 2 with duplicated conversion event
        eventFactory({
          timestamp: '2023-04-15T03:00:02.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:00.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd2',
        }),

        // device 3 with not releated events (eoi based)
        eventFactory({
          timestamp: '2023-04-15T03:01:01.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd3',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:01:00.000Z',
          field: 'eoi_variantId',
          value: 'variant1234',
          deviceId: 'd3',
        }),
      ];
      const actual = await run({
        events,
        rules: simpleRule,
        startDate: '2023-04-01T00:00:00.000Z',
        endDate: '2023-05-01T00:00:00.000Z',
      });
      expect(actual).toEqual([
        expect.objectContaining({
          conversionCount: 2,
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should handle the difference between visit and view', async () => {
      const events = [
        eventFactory({
          timestamp: '2023-04-15T03:00:02.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
        }),
      ];
      const actual = await run({
        events,
        rules: simpleRule,
        startDate: '2023-04-01T00:00:00.000Z',
        endDate: '2023-05-01T00:00:00.000Z',
      });
      expect(actual).toEqual([
        expect.objectContaining({
          visitorCount: 1,
          pageViewCount: 2,
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should handle the attribution window', async () => {
      const events = [
        // device 1 - inside the attribution window
        eventFactory({
          timestamp: '2023-04-20T00:00:00.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T00:00:00.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd1',
        }),

        // device 2 - outside the attribution window
        eventFactory({
          timestamp: '2023-04-07T00:00:00.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-04-01T00:00:00.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd2',
        }),
      ];
      const actual = await run({
        events,
        rules: simpleRule,
        startDate: '2023-04-01T00:00:00.000Z',
        endDate: '2023-05-01T00:00:00.000Z',
      });
      expect(actual).toEqual([
        expect.objectContaining({
          visitorCount: 2,
          pageViewCount: 2,
          conversionCount: 1,
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should handle the date interval', async () => {
      const events = [
        // inside the interval
        eventFactory({
          timestamp: '2023-04-02T00:00:00.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-01T00:00:00.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd1',
        }),

        // outside the interval, before
        eventFactory({
          timestamp: '2023-03-02T00:00:00.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-03-01T00:00:00.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd2',
        }),

        // outside the interval, after
        eventFactory({
          timestamp: '2023-05-02T00:00:00.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd3',
        }),
        eventFactory({
          timestamp: '2023-05-01T00:00:01.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1234',
          deviceId: 'd3',
        }),
      ];
      const actual = await run({
        events,
        rules: simpleRule,
        startDate: '2023-04-01T00:00:00.000Z',
        endDate: '2023-05-01T00:00:00.000Z',
      });
      expect(actual).toEqual([
        expect.objectContaining({
          visitorCount: 1,
          pageViewCount: 1,
          conversionCount: 1,
          variantId: 'variant1234',
        }),
      ]);
    });

    test('should handle aggregation by variant', async () => {
      const events = [
        // variant 1, device 1
        eventFactory({
          timestamp: '2023-04-15T03:00:05.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:04.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1',
          deviceId: 'd1',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:03.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant1',
          deviceId: 'd1',
        }),

        // variant 2, device 2
        eventFactory({
          timestamp: '2023-04-15T03:00:02.000Z',
          field: 'pageView_url',
          value: 'https://someshopifypage/cart',
          deviceId: 'd2',
        }),
        eventFactory({
          timestamp: '2023-04-15T03:00:01.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant2',
          deviceId: 'd2',
        }),

        // variant 3, device 2 (one conversion event counts for two variants)
        eventFactory({
          timestamp: '2023-04-15T03:00:00.000Z',
          field: 'splitURLTest_variantId',
          value: 'variant3',
          deviceId: 'd2',
        }),
      ];
      const actual = await run({
        events,
        rules: simpleRule,
        startDate: '2023-04-01T00:00:00.000Z',
        endDate: '2023-05-01T00:00:00.000Z',
      });
      expect(actual).toEqual([
        expect.objectContaining({
          visitorCount: 1,
          pageViewCount: 2,
          conversionCount: 1,
          variantId: 'variant1',
        }),
        expect.objectContaining({
          visitorCount: 1,
          pageViewCount: 1,
          conversionCount: 1,
          variantId: 'variant2',
        }),
        expect.objectContaining({
          visitorCount: 1,
          pageViewCount: 1,
          conversionCount: 1,
          variantId: 'variant3',
        }),
      ]);
    });
  });
});

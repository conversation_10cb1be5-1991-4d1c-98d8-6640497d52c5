const _get = require('lodash.get');
const { model: AccountModel } = require('../account/account.model');
const { model: LoginModel } = require('../login/login.model');
const { braintree, merchantAccountIds } = require('../../util/braintree');
const Translator = require('../../util/translator');

const Mailer = require('../../util/mailer');
const Templating = require('../../util/templating');
const BackofficeApiClient = require('../../services/backofficeApiClient');
const Region = require('../../util/region');
const {
  sendGeneratedAgencyReport,
  sendAgencyReportByDateEmails,
} = require('../../services/mail/agencySubUsageStat');
const redisClient = require('../../services/ioRedisAdapter');
const { sleep } = require('../../util/util');
const fraudPayment = require('../../services/fraudPayment');
const errors = require('../../util/braintree/errors.json');

const ADMIN_DOMAIN = process.env.om_new_admin_url;

const getAgencyAccountWithLogins = async (databaseId) => {
  return AccountModel.aggregate([
    { $match: { databaseId } },
    {
      $lookup: {
        localField: 'users.0.loginId',
        from: 'master_logins',
        foreignField: '_id',
        as: 'logins',
      },
    },
  ]);
};

const getAccountWithLogin = async (databaseId) => {
  const accounts = await AccountModel.aggregate([
    { $match: { databaseId } },
    {
      $lookup: {
        localField: 'users.0.loginId',
        from: 'master_logins',
        foreignField: '_id',
        as: 'login',
      },
    },
  ]);

  return { account: accounts[0], login: accounts[0].login[0] };
};

const getCustomerFor = async (account) => {
  const userId = account.id;

  const response = await BackofficeApiClient.get(`/payment/customerInfo/${userId}`);
  if (!response) {
    return false;
  }

  const customer = response.data.customer;
  customer.currency = Region.getCurrency(account.region);

  return customer;
};

const getBraintreeClientToken = async (_, __, { userId, log }) => {
  let token;
  let customer;
  const options = {};
  const account = (await AccountModel.getAccountsWithRegion([userId]))[0];

  try {
    customer = await getCustomerFor(account);
  } catch (e) {
    log.error(e, 'error during getting customer data');
  }

  if (!customer) {
    log.error(`No customer found for databaseId: ${userId}`);
  }

  if (customer.braintree_id) {
    options.customerId = customer.braintree_id;
  } else {
    const response = await BackofficeApiClient.post(`/payment/braintree/createCustomer/${userId}`);
    if (response) {
      options.customerId = response.data.id;
    }
  }
  options.merchantAccountId = merchantAccountIds(customer);

  try {
    token = await braintree.clientToken.generate(options);
  } catch (e) {
    log.error(e, 'error while getting braintree client token');
  }

  return { token: token.clientToken };
};

const getFlexiPayDetail = async (_, __, { req, userId, log }) => {
  if (!userId) {
    log.warn({ message: 'No databaseId', cookie: req.headers.cookie });
    return [];
  }

  try {
    const { data } = await BackofficeApiClient.get(`/payment/${userId}/flexiPayDetail`);

    if (!data.success) {
      log.warn('Unsuccessful getFlexiPayDetail query', data);
      return [];
    }

    return data;
  } catch (e) {
    log.error('Error in getFlexiPayDetail', { error: e.message, userId });
  }
};

const getDeclinedPaymentDetail = async (_, __, { userId, log }) => {
  try {
    const { data } = await BackofficeApiClient.get(`/payment/${userId}/declinedDetails`);

    if (!data.success) {
      return [];
    }

    return data;
  } catch (e) {
    log.error('Error in getDeclinedPaymentDetail', { error: e.message, userId });
  }
};

const cancelShopifyCharge = async (_, __, { userId, log }) => {
  const account = await AccountModel.findOne({
    databaseId: userId,
    'settings.shops': { $elemMatch: { type: 'shopify', active: 1, pay: 1 } },
  });

  if (!account) {
    log.warn('No paying shopify setting for cancelShopifyCharge for user');
    return { success: false };
  }

  try {
    log.info('Move user to FREE in backoffice, shopify cancel');
    const {
      data: { success },
    } = await BackofficeApiClient.post(`/payment/subscription/${userId}/cancel`, {
      method: 'shopify',
    });
    if (!success) {
      return { success: false };
    }
  } catch (e) {
    log.error(e, 'error during backoffice Shopify cancel');
    return { success: false };
  }

  return { success: true };
};

const changePayment = async (_, { method, paymentNonce }, { userId, log }) => {
  const account = await AccountModel.findOne({ databaseId: userId });

  if (!account) {
    log.warn('No account found at payment change!');
    return { success: false, message: 'No account found!' };
  }

  const data = { method, paymentNonce, optimonkId: account.databaseId };

  try {
    await BackofficeApiClient.post('/payment', data);
  } catch (e) {
    log.error(e, 'error during communicating with sales');
    return { success: false, message: 'Internal error!' };
  }

  return { success: true };
};

const requestPlanChange = async (_, { planName, period }, { userId, log }) => {
  const account = await AccountModel.findOne({ databaseId: userId });

  if (!account) {
    log.warn('No account found at payment change!');
    return { success: false, message: 'No account found!' };
  }

  const login = await LoginModel.findOne({ _id: account.users[0].loginId });

  Translator.setLocale('hu');

  const html = Templating.render('mail/backoffice-request-plan-change', {
    planName,
    period,
    databaseId: userId,
    email: login.email,
    firstName: login.firstName,
    lastName: login.lastName,
  });

  const mailConf = {
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Díjbekérő csomag módosítási kérelem',
    text: html,
    html,
  };

  await Mailer.sendMail(mailConf);

  return { success: true, message: 'Ticket submitted!' };
};

const changePackage = async (_, body, { req, userId, log }) => {
  const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress.split(':').pop();
  const account = await AccountModel.findOne({ databaseId: userId }, { 'settings.domains': 1 });

  const fraudParams = {
    ip,
    domains: account.settings.domains,
    userAgent: req.get('user-agent'),
  };

  if (await fraudPayment.isPossibleFraud(fraudParams)) {
    log.child({ method: 'change-package' }).warn({ message: 'Fraud detected', body, ip });
    await sleep(3000);
    return { success: false, message: 'Processor Declined' };
  }

  body.address = JSON.stringify(body.address);

  try {
    const result = await BackofficeApiClient.post(`/payment/create-order/${userId}`, body);
    const reasonCode = _get(result.data, 'response.transaction.processorResponseCode');

    if (result.data.response?.transaction) {
      // It means there was an error
      await fraudPayment.addIpToFailedPayment(ip);
    }

    if (reasonCode) {
      try {
        const { login } = await getAccountWithLogin(userId);
        const locale = login.locale;
        // eslint-disable-next-line
        const reason = _get(errors, `${locale}.${reasonCode}.text`, '');
        // eslint-disable-next-line
        const reasonDescription = _get(errors, `${locale}.${reasonCode}.description`, '');
        result.data.reason = reason;
        result.data.reasonDescription = reasonDescription;
      } catch (e) {
        log.error('Cannot get transaction error reason', e);
      }
    }
    return result.data;
  } catch (e) {
    log.error(e.message, e);
    return { success: false, message: e.message };
  }
};

const cancelSubscription = async (_, { reason }, { userId, log }) => {
  try {
    await BackofficeApiClient.post(`/payment/subscription/${userId}/cancel`, { reason });
  } catch (e) {
    log.error('Error during backoffice subscription cancel request', e);
    return { success: false, message: 'Error during subscription cancel request!' };
  }

  return { success: true, message: 'Subscription cancelled!' };
};

const keepSubscription = async (_, { reason, solution }, { userId, log }) => {
  try {
    await BackofficeApiClient.post(`/payment/subscription/${userId}/keep`, { reason, solution });
  } catch (e) {
    log.error('Error during backoffice subscription keep request', e);
    return { success: false };
  }

  return { success: true };
};

// First req during charge creation / updating process
const getShopifyConfirmUrl = async (
  _,
  { packageName, period, type, replacementBehavior, couponCode },
  { userId, log },
) => {
  const { login } = await getAccountWithLogin(userId);
  const region = login.region;
  const regionCode = region === 'Hungary' ? 'HU' : region === 'USA' ? 'EN' : 'DE';
  const packageSku = `Package-${regionCode}-${packageName}-${period}`;
  const returnUri = encodeURIComponent(
    `${ADMIN_DOMAIN}/${userId}/plan-settings?payment=shopify&action=return2&type=${type}`,
  );

  const query =
    `?userId=${userId}&packageSku=${packageSku}&returnUrl=${returnUri}` +
    `&type=${type}&replacementBehavior=${replacementBehavior}&couponCode=${couponCode}`;

  try {
    const response = await BackofficeApiClient.get(
      `/payment/shopify/charge-confirmation-url${query}`,
    );
    return { success: true, confirmUri: response.data.confirmationUrl };
  } catch (e) {
    log.error('Error during backoffice shopify create confirm url request', e.message, e.stack);
    return { error: e.message };
  }
};

const getShopifyCustomConfirmUrl = async (_, { type }, { userId, log }) => {
  const returnUri = encodeURIComponent(
    `${ADMIN_DOMAIN}/${userId}/plan-settings?payment=customshopify&action=return2&type=${type}`,
  );
  try {
    const response = await BackofficeApiClient.get(
      `/payment/shopify/custom-charge-confirmation-url?userId=${userId}&type=${type}&returnUrl=${returnUri}`,
    );
    return { success: true, confirmUri: response.data.confirmationUrl };
  } catch (e) {
    log.error('Error during backoffice custom shopify create confirm url request');
    return { success: false, error: e.message };
  }
};

// Second req during charge creation / updating process (after SY plan approving)
const checkShopifyChargeStatus = async (
  _,
  { chargeId, type = 'recurring', custom = 0 },
  { userId, log },
) => {
  try {
    const response = await BackofficeApiClient.get(
      `/payment/shopify/check-charge-status?userId=${userId}&chargeId=${chargeId}&type=${type}&custom=${custom}`,
    );
    return { success: response.data.status === 'active', error: response.error ?? null };
  } catch (e) {
    log.error('Error during backoffice shopify check charge status request');
    return { success: false, error: e.message };
  }
};

const validateCoupon = async (_, { couponCode }, { userId }) => {
  try {
    const response = await BackofficeApiClient.get(
      `/payment/coupon/validate?code=${couponCode}&databaseId=${userId}`,
    );

    return response.data;
  } catch (e) {
    return { success: false, errors: ['Not eligible to apply coupon!'] };
  }
};

const addAffiliateRelationshipByCouponCode = async (_, { couponCode }, { userId }) => {
  try {
    const response = await BackofficeApiClient.post(
      `/payment/coupon/addAffiliateRelationshipByCouponCode`,
      { code: couponCode, databaseId: userId },
    );
    return response.data;
  } catch (e) {
    return { success: false, errors: ['Error when creating affiliate partnership by coupon code'] };
  }
};

const getValidatedCouponByCode = async (_, { couponCode }, { userId }) => {
  try {
    const response = await BackofficeApiClient.get(
      `/payment/coupon/getValidatedCouponByCode?code=${couponCode}&databaseId=${userId}`,
    );

    return response.data;
  } catch (e) {
    return { success: false, errors: ['Not eligible to apply coupon!'] };
  }
};

const getValidActivatedCoupon = async (_, __, { userId }) => {
  try {
    const response = await BackofficeApiClient.get(
      `/payment/coupon/${userId}/activatedCouponDetails`,
    );

    return response.data;
  } catch (e) {
    return { success: false, errors: ['Not eligible to apply coupon!'] };
  }
};

const checkAndUpdateUsageLimitReached = async (databaseId) => {
  const usageReportKey = `usageReportRequest:${databaseId}`;
  const requested = await redisClient.get(usageReportKey);

  if (requested) return true;

  await redisClient.setex(usageReportKey, true, 60);
  return false;
};

const generateSubAccountsUsageReport = async (_, { databaseId }) => {
  const isLimitReached = await checkAndUpdateUsageLimitReached(databaseId);
  if (isLimitReached) {
    return { success: false, errorCode: 'LIMIT_REACHED' };
  }

  const agencyAccount = await getAgencyAccountWithLogins(databaseId);
  if (!agencyAccount[0]) {
    return { success: false, errorCode: 'NO_AGENCY_ACCOUNT' };
  }
  try {
    const response = await BackofficeApiClient.get(
      `/payment/get-subuser-usage-report/${databaseId}`,
    );

    if (response.data.success) {
      const { statistics } = response.data;
      await sendGeneratedAgencyReport(agencyAccount[0], { statistics });
      return { success: true };
    }
    return { success: false, errorCode: 'GENERATE_REPORT_ERROR' };
  } catch (e) {
    return { success: false, errors: ['Error when generating subaccount usage report'] };
  }
};

const sendAgencyReportByDate = async (_, { fromDate, toDate, emails }, { userId: databaseId }) => {
  const isLimitReached = await checkAndUpdateUsageLimitReached(databaseId);
  if (isLimitReached) {
    return { success: false, errorCode: 'LIMIT_REACHED' };
  }

  const agencyAccount = await getAgencyAccountWithLogins(databaseId);
  if (!agencyAccount[0]) {
    return { success: false, errorCode: 'NO_AGENCY_ACCOUNT' };
  }

  try {
    const response = await BackofficeApiClient.post(`/accounts/${databaseId}/agency-report`, {
      fromDate,
      toDate,
    });

    if (response?.data.success) {
      if (Object.keys(response.data.data).length) {
        await sendAgencyReportByDateEmails(emails, fromDate, toDate, response.data.data);
        return { success: true };
      }

      return { success: false, errorCode: 'NO_DATA_IN_RANGE' };
    }

    return { success: false, errorCode: 'GENERATE_REPORT_ERROR' };
  } catch (e) {
    return { success: false, errors: ['Error when generating subaccount usage report by date'] };
  }
};

module.exports = {
  Query: {
    getBraintreeClientToken,
    getFlexiPayDetail,
    getShopifyConfirmUrl,
    getShopifyCustomConfirmUrl,
    checkShopifyChargeStatus,
    validateCoupon,
    getValidatedCouponByCode,
    getValidActivatedCoupon,
    generateSubAccountsUsageReport,
    sendAgencyReportByDate,
    getDeclinedPaymentDetail,
  },
  Mutation: {
    cancelShopifyCharge,
    changePayment,
    requestPlanChange,
    changePackage,
    cancelSubscription,
    keepSubscription,
    addAffiliateRelationshipByCouponCode,
  },
};

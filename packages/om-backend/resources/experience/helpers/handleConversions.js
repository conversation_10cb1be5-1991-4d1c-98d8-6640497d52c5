const { model: ExperienceModel, ObjectId } = require('../experience.model');
const { logicallyDeleteExperience } = require('./removes');
const { queueCampaignChange } = require('../../../services/queue/changeLogQueue');
const { campaignChangeTypes } = require('../../changeLog/common');

const appendCampaignFrontendRules = async ({ campaign, frontendRules, userCampaignModel }) => {
  if (!frontendRules.length) return;

  await userCampaignModel.updateOne(
    { _id: campaign },
    { $push: { 'settings.frontendRules': { $each: frontendRules } } },
  );
};

const revertToSimpleCampaign = async (context) => {
  const { campaign, databaseId, userCampaignModel, userContext } = context;
  if (!campaign) return;

  const experiences = await ExperienceModel.find(
    { campaign, deletedAt: { $exists: false } },
    { frontendRules: 1 },
  );

  if (experiences.length === 1) {
    const [lastExperience] = experiences;
    const { frontendRules = [], _id: experienceId } = lastExperience;
    await Promise.all([
      appendCampaignFrontendRules({ campaign, frontendRules, userCampaignModel }),
      logicallyDeleteExperience(experienceId, databaseId),
    ]);

    queueCampaignChange({
      changeContext: {
        campaignId: ObjectId(campaign),
        experienceId: ObjectId(experienceId),
      },
      changeType: campaignChangeTypes.REMOVED_EXPERIENCE,
      userContext,
    });
  }
};

module.exports.revertToSimpleCampaign = revertToSimpleCampaign;

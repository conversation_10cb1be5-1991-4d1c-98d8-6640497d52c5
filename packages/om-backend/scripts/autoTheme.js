require('dotenv-flow').config({ silent: true });
const mongoose = require('mongoose');
const ThemeDetect = require('../services/themeDetect');

const { mongo_url } = process.env;

const domains = ['tutigumi.hu'];

const run = async () => {
  await mongoose.connect(mongo_url, { useNewUrlParser: true });
  const userId = 46;

  for (const domain of domains) {
    try {
      const { styles: result } = await ThemeDetect.getStylesAndData(userId, domain, false);
      console.log('@@@@@result', result);
      // TODO[fopi]
    } catch (e) {
      console.error(e);
    }
  }

  process.exit(0);
};

run();
